-- 相册管理表
CREATE TABLE `photo_album` (
  `album_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '相册ID',
  `offline_race_id` bigint(20) NOT NULL COMMENT '关联的线下赛赛事ID',
  `album_name` varchar(100) NOT NULL COMMENT '相册名称',
  `album_description` varchar(500) DEFAULT NULL COMMENT '相册描述',
  `photo_plus_album_ids` json NOT NULL COMMENT '谱时相册ID列表',
  `photographer_share_ratio` decimal(5,2) NOT NULL COMMENT '摄影师分成比例（百分比，如30表示30%）',
  `company_share_ratio` decimal(5,2) NOT NULL COMMENT '公司分成比例（百分比，如70表示70%）',
  `single_photo_price` decimal(10,2) NOT NULL COMMENT '单张照片价格（元）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '相册状态：0-禁用 1-启用',
  `cover_image_url` varchar(500) DEFAULT NULL COMMENT '相册封面图片URL',
  `sort_weight` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重，数值越大排序越靠前',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`album_id`),
  KEY `idx_offline_race_id` (`offline_race_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_weight` (`sort_weight`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='相册管理表';

-- 相册折扣配置表
CREATE TABLE `photo_album_discount` (
  `discount_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '折扣配置ID',
  `album_id` bigint(20) NOT NULL COMMENT '关联的相册ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `discount_ratio` decimal(5,2) DEFAULT NULL COMMENT '折扣比例（百分比，如90表示9折）',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣后单价（元）',
  `discount_type` tinyint(1) NOT NULL COMMENT '折扣类型：1-按比例折扣 2-固定价格',
  `discount_name` varchar(50) DEFAULT NULL COMMENT '折扣名称（如：3张9折、5张8折）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用 1-启用',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序权重，数值越小排序越靠前',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`discount_id`),
  KEY `idx_album_id` (`album_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_quantity` (`quantity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='相册折扣配置表';

-- 添加外键约束（可选）
-- ALTER TABLE `photo_album_discount` ADD CONSTRAINT `fk_photo_album_discount_album_id` FOREIGN KEY (`album_id`) REFERENCES `photo_album` (`album_id`) ON DELETE CASCADE;

-- 插入示例数据（可选）
-- INSERT INTO `photo_album` (`offline_race_id`, `album_name`, `album_description`, `photo_plus_album_ids`, `photographer_share_ratio`, `company_share_ratio`, `single_photo_price`, `status`, `cover_image_url`, `sort_weight`, `remark`, `tenant_id`) VALUES
-- (1, '2025北京马拉松相册', '2025年北京马拉松官方摄影相册', '["album001", "album002"]', 30.00, 70.00, 15.00, 1, 'https://example.com/cover.jpg', 100, '官方摄影师拍摄', 1);

-- INSERT INTO `photo_album_discount` (`album_id`, `quantity`, `discount_ratio`, `discount_type`, `discount_name`, `status`, `sort_order`, `remark`, `tenant_id`) VALUES
-- (1, 3, 90.00, 1, '3张9折', 1, 1, '购买3张照片享受9折优惠', 1),
-- (1, 5, 80.00, 1, '5张8折', 1, 2, '购买5张照片享受8折优惠', 1),
-- (1, 10, 70.00, 1, '10张7折', 1, 3, '购买10张照片享受7折优惠', 1);
