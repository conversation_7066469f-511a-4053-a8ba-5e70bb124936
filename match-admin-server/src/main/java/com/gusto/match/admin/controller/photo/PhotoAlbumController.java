package com.gusto.match.admin.controller.photo;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gusto.framework.core.bean.CommonResult;
import com.gusto.match.core.operatelog.OperateLog;
import com.gusto.match.core.satoken.AdminPermissionOfflineCode;
import com.gusto.match.core.satoken.StpAdminUtil;
import com.gusto.match.core.service.photo.PhotoAlbumDiscountService;
import com.gusto.match.core.service.photo.PhotoAlbumService;
import com.gusto.match.core.swagger.ApiVersion;
import com.gusto.match.core.swagger.CustomVersion;
import com.gusto.match.core.tenant.TenantEnable;
import com.gusto.match.model.entity.photo.PhotoAlbum;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;
import com.gusto.match.model.entity.photo.req.BgCreatePhotoAlbumDiscountReq;
import com.gusto.match.model.entity.photo.req.BgCreatePhotoAlbumReq;
import com.gusto.match.model.entity.photo.req.BgQueryPhotoAlbumReq;
import com.gusto.match.model.entity.photo.req.BgUpdatePhotoAlbumReq;
import com.gusto.match.model.entity.photo.rsp.PhotoAlbumDetailRsp;
import com.gusto.match.model.entity.photo.rsp.PhotoAlbumListRsp;
import com.gusto.match.model.enums.admin.OperateTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 相册管理 控制类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Tag(name = "相册管理")
@SaCheckLogin(type = StpAdminUtil.TYPE)
@Validated
@RestController
@RequestMapping("photo/album")
public class PhotoAlbumController {

    @Autowired
    private PhotoAlbumService photoAlbumService;

    @Autowired
    private PhotoAlbumDiscountService photoAlbumDiscountService;

    /**
     * 分页获取相册列表
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "分页获取相册列表")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_QUERY, type = StpAdminUtil.TYPE)
    @TenantEnable
    @GetMapping("query")
    public CommonResult<IPage<PhotoAlbumListRsp>> query(BgQueryPhotoAlbumReq req) {
        IPage<PhotoAlbum> page = photoAlbumService.getAlbumPage(
                req.getCurrent(), req.getSize(), req.getOfflineRaceId(), req.getStatus(), req.getAlbumName());
        
        IPage<PhotoAlbumListRsp> result = page.convert(album -> BeanUtil.copyProperties(album, PhotoAlbumListRsp.class));
        return CommonResult.success(result);
    }

    /**
     * 获取相册详情
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "获取相册详情")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_QUERY, type = StpAdminUtil.TYPE)
    @TenantEnable
    @GetMapping("detail/{albumId}")
    public CommonResult<PhotoAlbumDetailRsp> getDetail(
            @Parameter(description = "相册ID") @PathVariable Long albumId) {
        PhotoAlbum album = photoAlbumService.getAlbumById(albumId);
        List<PhotoAlbumDiscount> discounts = photoAlbumDiscountService.getDiscountsByAlbumId(albumId);
        
        PhotoAlbumDetailRsp result = BeanUtil.copyProperties(album, PhotoAlbumDetailRsp.class);
        if (CollUtil.isNotEmpty(discounts)) {
            List<PhotoAlbumDetailRsp.PhotoAlbumDiscountRsp> discountRsps = discounts.stream()
                    .map(discount -> BeanUtil.copyProperties(discount, PhotoAlbumDetailRsp.PhotoAlbumDiscountRsp.class))
                    .collect(Collectors.toList());
            result.setDiscounts(discountRsps);
        }
        
        return CommonResult.success(result);
    }

    /**
     * 创建相册
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "创建相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_CREATE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.CREATE, operateDesc = "创建相册")
    @PostMapping("create")
    public CommonResult<Long> create(@Valid @RequestBody BgCreatePhotoAlbumReq req) {
        PhotoAlbum album = BeanUtil.copyProperties(req, PhotoAlbum.class);
        Long albumId = photoAlbumService.createAlbum(album);
        return CommonResult.success(albumId);
    }

    /**
     * 更新相册
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "更新相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_UPDATE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.UPDATE, operateDesc = "更新相册")
    @PutMapping("update")
    public CommonResult<Void> update(@Valid @RequestBody BgUpdatePhotoAlbumReq req) {
        PhotoAlbum album = BeanUtil.copyProperties(req, PhotoAlbum.class);
        photoAlbumService.updateAlbum(album);
        return CommonResult.success();
    }

    /**
     * 删除相册
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "删除相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_DELETE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.DELETE, operateDesc = "删除相册")
    @DeleteMapping("delete/{albumId}")
    public CommonResult<Void> delete(
            @Parameter(description = "相册ID") @PathVariable Long albumId) {
        photoAlbumService.deleteAlbum(albumId);
        return CommonResult.success();
    }

    /**
     * 启用/禁用相册
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "启用/禁用相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_UPDATE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.UPDATE, operateDesc = "启用/禁用相册")
    @PutMapping("status/{albumId}")
    public CommonResult<Void> updateStatus(
            @Parameter(description = "相册ID") @PathVariable Long albumId,
            @Parameter(description = "状态：0-禁用 1-启用") @RequestParam Integer status) {
        photoAlbumService.updateAlbumStatus(albumId, status);
        return CommonResult.success();
    }

    /**
     * 创建相册折扣配置
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "创建相册折扣配置")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_UPDATE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.CREATE, operateDesc = "创建相册折扣配置")
    @PostMapping("discount/create")
    public CommonResult<Void> createDiscounts(@Valid @RequestBody BgCreatePhotoAlbumDiscountReq req) {
        List<PhotoAlbumDiscount> discounts = req.getDiscounts().stream()
                .map(item -> BeanUtil.copyProperties(item, PhotoAlbumDiscount.class))
                .collect(Collectors.toList());
        photoAlbumDiscountService.createDiscounts(req.getAlbumId(), discounts);
        return CommonResult.success();
    }

    /**
     * 启用/禁用折扣配置
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "启用/禁用折扣配置")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_UPDATE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.UPDATE, operateDesc = "启用/禁用折扣配置")
    @PutMapping("discount/status/{discountId}")
    public CommonResult<Void> updateDiscountStatus(
            @Parameter(description = "折扣配置ID") @PathVariable Long discountId,
            @Parameter(description = "状态：0-禁用 1-启用") @RequestParam Integer status) {
        photoAlbumDiscountService.updateDiscountStatus(discountId, status);
        return CommonResult.success();
    }

    /**
     * 删除折扣配置
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "删除折扣配置")
    @SaCheckPermission(value = AdminPermissionOfflineCode.OFFLINE_RACE_DELETE, type = StpAdminUtil.TYPE)
    @TenantEnable
    @OperateLog(operateType = OperateTypeEnum.DELETE, operateDesc = "删除折扣配置")
    @DeleteMapping("discount/delete/{discountId}")
    public CommonResult<Void> deleteDiscount(
            @Parameter(description = "折扣配置ID") @PathVariable Long discountId) {
        photoAlbumDiscountService.deleteDiscount(discountId);
        return CommonResult.success();
    }
}
