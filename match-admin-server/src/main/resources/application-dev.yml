server:
  port: 10001
  servlet:
    context-path: /bg

spring:
  config:
    activate:
      on-profile:
        - dev

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  application:
    name: match-admin-server

  datasource:
    name: datasource
    url: *********************************************************************************************************************************************************************************************************************************
    username: gusto_dev_java
    password: X7Hq4)I_Xd5v@k)fs#1$
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 20
      max-active: 200
      max-wait: 30000
      min-idle: 30
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 200
      remove-abandoned: false
      remove-abandoned-timeout: 1800
      log-abandoned: true
      filter:
        # 配置用于监控慢查询 （参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_StatFilter）
        stat:
          enabled: true
          slow-sql-millis: 1000
          log-slow-sql: true
          db-type: mysql
          # 合并语句 优化sql统计功能
          merge-sql: true
        # 日志 使用logback为slf4j (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_LogFilter)
        slf4j:
          enabled: true
          statement-executable-sql-log-enable: true
          statement-parameter-set-log-enabled: false
          statement-execute-query-after-log-enabled: false
          statement-prepare-after-log-enabled: false
          statement-execute-after-log-enabled: false
          statement-prepare-call-after-log-enabled: false
          statement-close-after-log-enabled: false
          statement-execute-batch-after-log-enabled: true
        # 防火墙用于配置预防sql注入 (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE-wallfilter)
        wall:
          enabled: true
          db-type: mysql
          config:
            drop-table-allow: false
            multi-statement-allow: true
  redis:
    database: 0
    host: ************
    port: 36379
    password: gusto123$
    timeout: 3s
    redisson:
      pool-size: 64
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  kafka:
    # 生产者配置
    producer:
      bootstrapServers: localhost:9092    # 服务连接地址
      retries: 0                          # 重试次数, 默认 0 不重试
      compression-type: gzip              # 压缩算法
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        interceptor.classes: com.gusto.match.core.handler.TraceProducerMethodInterceptor
    # 消费者配置
    consumer:
      bootstrap-servers: localhost:9092   # 服务连接地址
      group-id: mygroup                   # 消息组的名称
      auto-offset-reset: latest           # 初始化offset的策略
      enable-auto-commit: true
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        interceptor.classes: com.gusto.match.core.handler.TraceConsumerMethodInterceptor
        spring:
          json:
            trusted:
              packages: com.gusto.match.model.kafka.message
    properties:
      request.timeout.ms: 3000            # 超时时间
      linger.ms: 0                        # 指定 Batch 以时间为策略, 表示延迟多久才发送一次. 0 表示不停留马上发送
      spring:
        json:
          trusted:
            packages: com.gusto.match.model.kafka.message

jetcache:
  statIntervalMinutes: 30
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 0
        maxIdle: 8
      uri: redis://:gusto123$@************:36379/1

logging:
  config: classpath:logback-spring.xml
  level:
    org.springframework.web: debug
    druid.sql.Statement: debug
    com.gusto: debug
    com.gusto.framework: debug
    com.gusto.match: debug
    com.gusto.match.dao: debug
    org.apache.kafka.clients.NetworkClient: error
  file:
    # 日志路径
    path: ./logs
    # 日志名字
    name: match-admin-server
    # 日志保留存活天数
    maxHistory: 7
    # 单日志大小(用于分割)
    maxFileSize: 100MB
    # 总日志大小(用于计算文件数量 number = totalSizeCap/maxFileSize )
    totalSizeCap: 500MB

elasticjob:
  regCenter:
    serverLists: localhost:2181
    namespace: elasticjob-lite-match-admin-server
  jobs:
    UserRunErrorStatJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.UserRunErrorStatJob
      cron: 0 0 3 * * ?
      shardingTotalCount: 1
    OfflineUserSignCancelJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OfflineUserSignCancelJob
      cron: 0 * * * * ?
      shardingTotalCount: 1
    OrderShipInfoUpdateJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OrderShipInfoUpdateJob
      cron: 0 0 2 * * ?
      shardingTotalCount: 1
    OfflineSelectNumberRecordCancelJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OfflineSelectNumberRecordCancelJob
      cron: 0 * * * * ?
      shardingTotalCount: 1
    AppPushUpdateStatJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.AppPushUpdateStatJob
      cron: 0 0 10 * * ?
      shardingTotalCount: 1
    OnlineUserSignCancelJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OnlineUserSignCancelJob
      cron: '0 */5 * * * ?'
      shardingTotalCount: 1
    OnlineRaceEndOrderJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OnlineRaceEndOrderJob
      cron: '0 */5 * * * ?'
      shardingTotalCount: 1
    ChallengeEndOrderJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.ChallengeEndOrderJob
      cron: '0 */5 * * * ?'
      shardingTotalCount: 1
    ChallengeUserSignRefundJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.ChallengeUserSignRefundJob
      cron: '0 */5 * * * ?'
      shardingTotalCount: 1
    AppPushUpdateGeTuiSchedulePushStateJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.AppPushUpdateGeTuiSchedulePushStateJob
      cron: '0 */5 * * * ?'
      shardingTotalCount: 1
    ClubCacheRecommendListJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.ClubCacheRecommendListJob
      cron: 0 0 4 * * ?
      shardingTotalCount: 1
    UserBuyPointRankStatJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.UserBuyPointRankStatJob
      cron: 0 0 2 15 * ?
      shardingTotalCount: 1
    UserOfflinePointRankStatJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.UserOfflinePointRankStatJob
      cron: 0 0 3 15 * ?
      shardingTotalCount: 1
    # TODO race update config
    OnlineRaceEndNotifyJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.OnlineRaceEndNotifyJob
      cron: 0 0 9 * * ?
      shardingTotalCount: 1
    ChallengeStartNotifyJob:
      overwrite: true
      elasticJobClass: com.gusto.match.admin.job.ChallengeStartNotifyJob
      cron: 0 0 12 * * ?
      shardingTotalCount: 1
    # TODO race update config
    #    OfflineUserSignRefundJob:
    #      overwrite: true
    #      elasticJobClass: com.gusto.match.admin.job.OfflineUserSignRefundJob
    #      cron: '0 */2 * * * ?'
    #      shardingTotalCount: 1
#    OnlineUserSignRefundJob:
#      overwrite: true
#      elasticJobClass: com.gusto.match.admin.job.OnlineUserSignRefundJob
#      cron: '30 */2 * * * ?'
#      shardingTotalCount: 1
#    CommunityStatJob:
#      overwrite: true
#      elasticJobClass: com.gusto.match.admin.job.CommunityStatJob
#      cron: 0 0 4 * * ?
#      shardingTotalCount: 1
#    AppFeedbackStatJob:
#      overwrite: true
#      elasticJobClass: com.gusto.match.admin.job.AppFeedbackStatJob
#      cron: 0 0 9 * * ?
#      shardingTotalCount: 1

#forest:
#  backend: okhttp3 # 后端HTTP框架（默认为 okhttp3）
#  max-connections: 500 # 连接池最大连接数（默认为 500）
#  max-route-connections: 500 # 每个路由的最大连接数（默认为 500）
#  timeout: 3000 # 请求超时时间，单位为毫秒（默认为 3000）
#  connect-timeout: 3000 # 连接超时时间，单位为毫秒（默认为 timeout）
#  read-timeout: 3000 # 数据读取超时时间，单位为毫秒（默认为 timeout）
#  max-retry-count: 3 # 请求失败后重试次数（默认为 0 次不重试）
#  ssl-protocol: TLSv1.3 # 单向验证的HTTPS的默认SSL协议（默认为 SSLv3）
#  logEnabled: true # 打开或关闭日志（默认为 true）
#  log-request: true # 打开/关闭Forest请求日志（默认为 true）
#  log-response-status: true # 打开/关闭Forest响应状态日志（默认为 true）
#  log-response-content: true # 打开/关闭Forest响应内容日志（默认为 false）
#  variables:
#    pushApiUrl: https://pushdevapi.gusto.cn
#    payPalApiUrl: https://api-m.sandbox.paypal.com

# Sa-Token配置
sa-token:
  enable: true
  # token名称 (同时也是cookie名称)
  token-name: gutoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  # 是否尝试从header里读取token
  is-read-header: true

management:
  server:
    port: 10011
  endpoints:
    web:
      exposure:
        include:
          - "prometheus"
          - "info"
  metrics:
    tags:
      application: ${spring.application.name}

gusto:
  xss:
    enable: true
    excludeUrls:
      - "/bg/offline/document/create"
      - "/bg/offline/document/update"
      - "/bg/shop/goods/create"
      - "/bg/shop/goods/update"
      - "/bg/online/race/update"
      - "/bg/online/race/create"
      - "/bg/new/survey/create"
      - "/bg/new/survey/update"
  error-code:
    logLevelMap:
      5000: WARN
      11004: WARN
      11005: WARN
  tenant:
    enable: true
    ignore-urls:
      - /bg/express/notify/zto
      - /bg/admin/login/mobile
      - /bg/admin/login/mobile/2
      - /bg/admin/login/magic
      - /bg/offline/race/list/domain
      - /bg/community/**
      - /bg/quiz/list
      - /bg/shop/goods/relation/**
      - /bg/shop/goods/query
      - /bg/offline/user/sign/review/check/qualification
      - /bg/offline/user/sign/review/check/previous/result
      - /bg/wx/cp/**
      - /bg/tag/**
      - /bg/new/survey/**
      - /bg/offline/survey/**
      - /bg/admin/current/tenant/list
      - /bg/admin/switch/tenant
      - /bg/push/** # 仅测试用
    include-tables:
      - offline_bibno
      - offline_bibno_assign_job
      - offline_bibno_generate_job
      - offline_bibno_rule
      - offline_channel
      - offline_document
      - offline_invite_code
      - offline_invite_code_create_job
      - offline_project
      - offline_race
      - offline_team
      - offline_user_bibno_import_job
      - offline_user_result
      - offline_user_result_import_job
      - offline_user_sign
      - offline_user_sign_import_ballot_job
      - offline_user_sign_import_job
      - offline_user_sign_import_refund_job
      - order_info
      - match_admin
      - offline_timing
      - offline_invite_code_batch_invalid_job
      - offline_select_number_record
      - offline_bibno_update_star_job

oss:
  qiniu:
    accessKey: op_iX9Uq6cnL0rfiy-HJ8cWCtbxL-XU8Q2O_Agke
    secretKey: pHdrlhsRbVmPR0Ahh9S688k-a8mYcV0jzAVMXQ4H

springdoc:
  swagger-ui:
    tags-sorter: alpha
  #    operations-sorter: alpha
  #  default-flat-param-object: true
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.gusto.match

# TODO photo update config
photo:
  photo-plus:
    api-host: https://testwxplus.plusx.cn
    salt: cd721e2ce6017f39