package com.gusto.match.model.entity.sunshine;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目绑定套餐
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
public class SunshineProjectPackage implements Serializable {
    private static final long serialVersionUID = 6538403112117771188L;

    @Schema(description = "套餐名称")
    private String name;

    @Schema(description = "排序，数值越大越靠前")
    private Integer sort;

    @Schema(description = "商品ID列表")
    private List<Long> goodsIds;
}
