package com.gusto.match.model.entity.payment.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PaymentPayPalNotifyReq {

    @Schema(description = "id: WH-3WH75389NV364153B-3D3753066E0431319")
    private String id;

    @Schema(description = "event_type: 1.0")
    private String eventVersion;

    @Schema(description = "create_time: 2025-04-29T03:23:22.287Z")
    private String createTime;

    @Schema(description = "resource_type: checkout-order")
    private String resourceType;

    @Schema(description = "resource_version: 2.0")
    private String resourceVersion;

    @Schema(description = "event_type: CHECKOUT.ORDER.APPROVED")
    private String eventType;

    @Schema(description = "summary: An order has been approved by buyer")
    private String summary;

    @Schema(description = "resource")
    private Object resource;

}
