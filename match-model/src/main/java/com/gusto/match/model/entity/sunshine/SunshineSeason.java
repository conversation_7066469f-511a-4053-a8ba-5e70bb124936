package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 赛事赛季
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sunshine_season")
public class SunshineSeason extends BaseEntity {

    private static final long serialVersionUID = -669883643584357336L;

    @Schema(description = "赛季ID")
    @TableId(value = "season_id", type = IdType.AUTO)
    private Long seasonId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "封面图片")
    private String coverImage;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "详情图")
    private String detailImage;

    @Schema(description = "Q&A")
    private String question;

    @Schema(description = "报名开始时间")
    private Instant signStartTime;

    @Schema(description = "报名截止时间")
    private Instant signEndTime;

    @Schema(description = "赛事开始时间")
    private Instant startTime;

    @Schema(description = "赛事截止时间")
    private Instant endTime;

    @Schema(description = "上线状态：0-默认 1-上线 2-下线")
    private Integer state;

}
