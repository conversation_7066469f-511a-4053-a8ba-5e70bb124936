package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 分页获取动态列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class GetCommunityPostPageReq {

    @Parameter(description = "话题ID")
    @Positive
    private Long topicId;

    @Parameter(description = "作者类型：0-默认 1-用户 2-官方")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer authorType = 1;

    @Parameter(description = "排序类型：0-默认 1-最热 2-最新")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sortType = 1;

    @Parameter(description = "标签ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tagId = 0L;

    @Parameter(description = "current")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long current = 1L;

    @Parameter(description = "size")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long size = 10L;

}
