package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseUserSign;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 赛事-用户报名
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sunshine_user_sign", autoResultMap = true)
public class SunshineUserSign extends BaseUserSign {
    private static final long serialVersionUID = 2758209049515473690L;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "号码前缀")
    private String bibnoSub;

    @Schema(description = "号码数字长度")
    private Integer bibnoLength;

    @Schema(description = "号码")
    private Long bibno;

    @Schema(description = "报名卡ID")
    private Long userCardId;

    @Schema(description = "是否修改过号码")
    private Boolean modifyBibno;

    @Schema(description = "是否删除")
    private Boolean deleted;

    @Schema(description = "订单ID列表")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> orderIds;

    @Schema(description = "完赛状态：0-默认 1-未挑战 2-挑战成功 3-挑战失败")
    private Integer finishedState;

    @Schema(description = "完赛时间")
    private Instant finishedTime;

    @Schema(description = "购买订单序号")
    private Integer signPackageIndex;

    @Schema(description = "挑战地点")
    private String challengeLocation;

    @Schema(description = "报名套餐名称")
    private String signPackageName;

    @Schema(description = "报名套餐商品ID列表")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> signPackageGoodsIds;

    @Schema(description = "监护人")
    private String guardiansName;

    @Schema(description = "监护人身份证")
    private String guardiansCardNumber;

}
