package com.gusto.match.model.entity.challenge.dto;

import com.gusto.match.model.entity.order.dto.OrderDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 挑战赛用户报名退款记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Data
public class BgUserSignRefundRecordDTO {

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "操作人类型：0-默认 1-用户 2-管理员 3-系统")
    private Integer operatorType;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作人IP")
    private String operatorIp;

    @Schema(description = "退款记录类型：0-默认 1-退赛 2-退差价")
    private Integer recordType;

    @Schema(description = "应退款金额，单位元")
    private BigDecimal refundAmount;

    @Schema(description = "实退款金额，单位元")
    private BigDecimal actualRefundAmount;

    @Schema(description = "平台支付单号")
    private String tradeNo;

    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "商户退款单号")
    private String outRefundId;

    @Schema(description = "平台退款单号")
    private String refundId;

    @Schema(description = "退款状态：0-默认 1-已申请 2-已退款 3-退款中 4-退款失败")
    private Integer refundState;

    @Schema(description = "退款时间")
    private Long refundTime;

    @Schema(description = "退款类型：0-默认 1-系统 2-手动")
    private Integer refundType;

    @Schema(description = "管理员备注")
    private String adminRemark;

    @Schema(description = "转账退款账户名")
    private String refundAccount;

    @Schema(description = "退款账户名")
    private String refundName;

    @Schema(description = "创建")
    private Long createTime;

    @Schema(description = "订单明细列表")
    private List<OrderDetailDTO> orderDetailList;

}
