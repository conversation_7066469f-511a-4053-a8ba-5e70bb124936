package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.challenge.dto.ChallengeRecordMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取陪跑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
public class ChallengeUserSignGetMemberListGroupRsp {

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "陪跑活动开始时间")
    private Long startTime;

    @Schema(description = "陪跑成员列表")
    private List<ChallengeRecordMemberDTO> memberList;

}
