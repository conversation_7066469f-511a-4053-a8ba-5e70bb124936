package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 用户赛事成绩（个人限时赛）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sunshine_user_time_result")
public class SunshineUserTimeResult extends BaseEntity {
    private static final long serialVersionUID = 6047551085668377401L;

    @Schema(description = "成绩ID")
    @TableId(value = "result_id", type = IdType.AUTO)
    private Long resultId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "挑战目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际挑战距离，单位米")
    private Double actualDistance;

    @Schema(description = "时长，单位秒")
    private Long duration;

    @Schema(description = "跑步开始时间")
    private Instant startTime;

    // enum UserTimeResultState
    @Schema(description = "挑战状态：0-默认 1-挑战成功")
    private Integer state;

    // 挑战成功时那一条跑步记录的跑步开始时间加时长
    @Schema(description = "完赛时间")
    private Instant finishTime;

    @Schema(description = "平均配速")
    private Double averagePace;
}
