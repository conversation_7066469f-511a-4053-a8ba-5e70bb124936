package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取祝福列表
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class ChallengeRecordGetBlessingListReq {

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "current")
    private Long current;

    @Schema(description = "size")
    private Long size;

}
