package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取祝福列表
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class ChallengeRecordGetBlessingListRsp {

    @Schema(description = "祝福ID")
    private Long blessId;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "祝福")
    private String blessing;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "挑战者回复")
    private String reply;

    @Schema(description = "回复时间")
    private Long replyTime;

}
