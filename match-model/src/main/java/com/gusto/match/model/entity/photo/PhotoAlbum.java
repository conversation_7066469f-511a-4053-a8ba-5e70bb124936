package com.gusto.match.model.entity.photo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 相册管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "photo_album", autoResultMap = true)
public class PhotoAlbum extends BaseTenantEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "相册ID")
    @TableId(type = IdType.AUTO)
    private Long albumId;

    @Schema(description = "关联的线下赛赛事ID")
    private Long offlineRaceId;

    @Schema(description = "相册名称")
    private String albumName;

    @Schema(description = "相册描述")
    private String albumDescription;

    @Schema(description = "谱时相册ID列表")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> photoPlusAlbumIds;

    @Schema(description = "摄影师分成比例（百分比，如30表示30%）")
    private BigDecimal photographerShareRatio;

    @Schema(description = "公司分成比例（百分比，如70表示70%）")
    private BigDecimal companyShareRatio;

    @Schema(description = "单张照片价格（元）")
    private BigDecimal singlePhotoPrice;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "相册封面图片URL")
    private String coverImageUrl;

    @Schema(description = "排序权重，数值越大排序越靠前")
    private Integer sortWeight;

    @Schema(description = "备注信息")
    private String remark;

    /**
     * 检查分成比例是否合理
     */
    public boolean isShareRatioValid() {
        if (photographerShareRatio == null || companyShareRatio == null) {
            return false;
        }
        return photographerShareRatio.add(companyShareRatio).compareTo(BigDecimal.valueOf(100)) == 0;
    }

    /**
     * 检查相册是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
}
