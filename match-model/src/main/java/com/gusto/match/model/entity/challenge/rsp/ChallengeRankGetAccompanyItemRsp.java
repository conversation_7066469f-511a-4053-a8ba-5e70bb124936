package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 获取陪跑人数榜前三
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
public class ChallengeRankGetAccompanyItemRsp implements Serializable {

    private static final long serialVersionUID = -1844594457814833442L;

    @Schema(description = "排名，为0时展示100+")
    private Integer rank;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "完赛时间")
    private Long finishedTime;

    @Schema(description = "值")
    private String value;

}
