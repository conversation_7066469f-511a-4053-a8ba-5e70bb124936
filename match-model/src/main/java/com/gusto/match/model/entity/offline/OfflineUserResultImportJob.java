package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛用户成绩导入任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_result_import_job", autoResultMap = true)
public class OfflineUserResultImportJob extends BaseTenantEntity {

    private static final long serialVersionUID = -8885866736641981800L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "导入总数")
    private Integer totalCount;

    @Schema(description = "在名单中的新增总数")
    private Integer inFileInsertCount;

    @Schema(description = "在名单中的更新总数")
    private Integer inFileUpdateCount;

    @Schema(description = "不在名单中的新增总数")
    private Integer notInFileInsertCount;

    @Schema(description = "不在名单中的更新总数")
    private Integer notInFileUpdateCount;

    @Schema(description = "不在名单中的移除总数")
    private Integer notInFileRemoveCount;

    @Schema(description = "导入方式：0-默认 1-全新导入 2-部分导入")
    private Integer importType;

}
