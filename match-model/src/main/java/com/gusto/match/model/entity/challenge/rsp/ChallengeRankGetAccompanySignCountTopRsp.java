package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 获取陪跑人数榜前三
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
public class ChallengeRankGetAccompanySignCountTopRsp implements Serializable {

    private static final long serialVersionUID = 7198766782364956995L;

    @Schema(description = "排名列表")
    private List<ChallengeRankGetAccompanyItemRsp> rankList;

}
