package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * <p>
 * 创建评论
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateCommunityCommentReq {

    @Schema(description = "动态ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long postId;

    @Schema(description = "回复评论ID")
    private Long toCommentId = 0L;

    @Schema(description = "内容，限制300字", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 300)
    private String content;

}
