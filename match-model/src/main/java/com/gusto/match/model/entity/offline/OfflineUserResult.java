package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <p>
 * 线下赛用户成绩
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_result", autoResultMap = true)
public class OfflineUserResult extends BaseTenantEntity {

    private static final long serialVersionUID = 2502325204582203535L;

    // 报名信息

    @Schema(description = "成绩ID")
    @TableId(type = IdType.AUTO)
    private Long resultId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "性别：0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户ID")
    private Long signId;

    @Schema(description = "支持跑团ID")
    private Long supportClubId;

    // 成绩信息

    @Schema(description = "净成绩，单位秒")
    private Long pureDuration;

    @Schema(description = "净项目排名")
    private Integer pureProjectRank;

    @Schema(description = "净性别排名")
    private Integer pureSexRank;

    @Schema(description = "净年龄排名")
    private Integer pureAgeRank;

    @Schema(description = "枪声成绩，单位秒")
    private Long gunDuration;

    @Schema(description = "枪声项目排名")
    private Integer gunProjectRank;

    @Schema(description = "枪声性别排名")
    private Integer gunSexRank;

    @Schema(description = "枪声年龄排名")
    private Integer gunAgeRank;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "累计爬升，单位米")
    private Double ascent;

    @Schema(description = "净分段用时列表，<分段序号(从1开始)，分段用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> purePartTimeDurationList;

    @Schema(description = "枪声分段用时列表，<分段序号(从1开始)，分段用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> gunPartTimeDurationList;

    @Schema(description = "净累计用时列表，<累计序号(从1开始)，累计用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> pureAddTimeDurationList;

    // 其他信息

    @Schema(description = "项目人数")
    private Integer projectCount;

    @Schema(description = "性别人数")
    private Integer sexCount;

    @Schema(description = "年龄人数")
    private Integer ageCount;

    @Schema(description = "年龄段")
    private String ageRange;

    @Schema(description = "管理员备注")
    private String adminRemark;

    @Schema(description = "队伍ID")
    private Long teamId;

    @Schema(description = "队伍名称")
    private String teamName;

    @Schema(description = "是否队长")
    private Boolean captain;

    @Schema(description = "队伍用时，单位秒")
    private Long teamDuration;

    @Schema(description = "队伍成员")
    private String teamMemberList;

    @Schema(description = "队伍排名")
    private Integer teamRank;

    // 第三方信息 TODO offline add sql

    @Schema(description = "第三方ID")
    private Long thirdId;

}
