package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 教练申请请求
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data

public class SubmitCoachApplicationReq {
    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realName;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(description = "性别：1-男 2-女", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sex;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "开始跑步时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long runStartTime;

    @Schema(description = "自我介绍", requiredMode = Schema.RequiredMode.REQUIRED)
    private String introduce;

    @Schema(description = "教练编号")
    private String coachNumber;
}
