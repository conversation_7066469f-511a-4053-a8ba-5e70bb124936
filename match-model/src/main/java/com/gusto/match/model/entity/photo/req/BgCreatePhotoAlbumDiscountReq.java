package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 后台-创建相册折扣配置请求
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgCreatePhotoAlbumDiscountReq {

    @Schema(description = "相册ID", required = true)
    @NotNull(message = "相册ID不能为空")
    private Long albumId;

    @Schema(description = "折扣配置列表", required = true)
    @NotEmpty(message = "折扣配置列表不能为空")
    @Valid
    private List<PhotoAlbumDiscountItem> discounts;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PhotoAlbumDiscountItem {

        @Schema(description = "购买数量", required = true)
        @NotNull(message = "购买数量不能为空")
        private Integer quantity;

        @Schema(description = "折扣类型：1-按比例折扣 2-固定价格", required = true)
        @NotNull(message = "折扣类型不能为空")
        private Integer discountType;

        @Schema(description = "折扣比例（百分比，如90表示9折）")
        private java.math.BigDecimal discountRatio;

        @Schema(description = "折扣后单价（元）")
        private java.math.BigDecimal discountPrice;

        @Schema(description = "折扣名称（如：3张9折、5张8折）")
        private String discountName;

        @Schema(description = "是否启用：0-禁用 1-启用")
        private Integer status = 1;

        @Schema(description = "备注信息")
        private String remark;
    }
}
