package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-23
 */
@Data
public class UserTimeResultDTO {
    @Schema(description = "时长，单位秒")
    private Long duration;

    @Schema(description = "完赛时间")
    private Long finishedTime;

    @Schema(description = "实际挑战距离，单位米")
    private Double actualDistance;
}
