package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-训练文章统计数据 响应类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data

public class TrainingArticleStaticRsp {

    @Schema(description = "文章浏览次数")
    private Long view;

    @Schema(description = "文章分享次数")
    private Long share;

    @Schema(description = "文章发布数")
    private Long articleSum;
}
