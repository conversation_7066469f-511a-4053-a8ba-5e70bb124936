package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取回复祝福列表
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class ChallengeRecordGetBlessingReplyPageItemRsp {

    @Schema(description = "祝福ID")
    private Long blessId;

    @Schema(description = "挑战者昵称")
    private String nickname;

    @Schema(description = "挑战者头像")
    private String avatar;

    @Schema(description = "挑战者回复")
    private String reply;

    @Schema(description = "回复时间")
    private Long replyTime;

    @Schema(description = "回复是否已读")
    private Boolean replyRead;

}
