package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class CommunityPostDTO {

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "作者类型：0-默认 1-用户 2-官方")
    private Integer authorType;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容，给官方动态列表用的，用户动态列表用title")
    private String content;

    @Schema(description = "图片列表，限制9张")
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "分享量")
    private Integer shareCount;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "是否点赞")
    private Boolean like;

    @Schema(description = "发布时间")
    private Long postTime;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

    @Schema(description = "最后评论时间")
    private Long lastReplyTime;

}
