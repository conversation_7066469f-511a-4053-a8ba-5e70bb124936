package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 社区话题
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_topic")
public class CommunityTopic extends BaseEntity {

    private static final long serialVersionUID = -5593431726480303663L;

    @Schema(description = "话题ID")
    @TableId(type = IdType.AUTO)
    private Long topicId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "背景色")
    private String backgroundColor;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛")
    private Integer primaryCategory;

    @Schema(description = "二级分类：0-默认 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer secondaryCategory;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "真实点击量")
    private Integer realClickCount;

    @Schema(description = "随机增加起始值，设置时需大于0")
    private Integer randomStart = 0;

    @Schema(description = "随机增加结束值，设置时需大于randomStart")
    private Integer randomEnd = 0;

    @Schema(description = "是否热门")
    private Boolean hot;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布")
    private Integer publishSetting;

    @Schema(description = "发布时间")
    private Instant publishTime;

}
