package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 修改点赞
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityChangeLikeReq {

    @Schema(description = "对象类型：0-默认 1-动态 2-评论")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "是否点赞")
    private Boolean like;

}
