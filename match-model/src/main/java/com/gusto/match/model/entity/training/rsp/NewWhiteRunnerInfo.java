package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/3/3
 */
@Data

public class NewWhiteRunnerInfo {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "教练名字")
    private String coachName;

    @Schema(description = "当前小白数量")
    private Integer whiteRunner;

    @Schema(description = "新增小白数量")
    private Integer newWhiteRunner;

    @Schema(description = "教练等级")
    private Integer level;

    @Schema(description = "是否锁定")
    private Boolean locked;
}
