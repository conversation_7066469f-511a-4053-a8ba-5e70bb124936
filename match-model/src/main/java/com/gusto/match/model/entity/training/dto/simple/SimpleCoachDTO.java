package com.gusto.match.model.entity.training.dto.simple;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/15
 */
@Data

public class SimpleCoachDTO {
    @Schema(description = "获奖经历")
    private List<String> awardList;

    @Schema(description = "相关资质")
    private List<String> qualificationList;

    @Schema(description = "是否隐藏自己的训练数据")
    private Boolean hide;
}
