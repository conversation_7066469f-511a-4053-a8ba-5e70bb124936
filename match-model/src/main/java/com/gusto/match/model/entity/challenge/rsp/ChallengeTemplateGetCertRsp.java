package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取完赛证书渲染数据
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeTemplateGetCertRsp {

    @Schema(description = "是否可以查看证书")
    private Boolean viewCert;

    @Schema(description = "不能查看证书的原因")
    private String viewCertReason;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "证书背景图")
    private String certBackgroundImage;

    @Schema(description = "发起陪跑证书模板背景图")
    private String ownBackgroundImage;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "完赛里程，单位米")
    private Double finishedDistance;

    @Schema(description = "完赛用时，单位秒")
    private Long finishedDuration;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "完赛日期")
    private String finishedDate;

    @Schema(description = "平均配速")
    private String averagePace;

    @Schema(description = "陪跑总人数")
    private Integer totalMemberCount;

    @Schema(description = "陪跑总距离")
    private Double totalMemberDistance;

    @Schema(description = "陪跑名单")
    private List<ChallengeTemplateGetCertMemberRsp> memberList;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "证书印章图片")
    private String certStampImage;

}
