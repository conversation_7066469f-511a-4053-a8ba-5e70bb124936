package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户教练列表子项
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data

public class UserCoachListElementDTO {
    @Schema(description = "教练ID")
    private Integer coachId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "关注数量")
    private Integer followCount;

    @Schema(description = "是否关注 1 关注 2 没有")
    private Integer attention;

    @Schema(description = "教练等级 0-默认 1-准教练 2-初级教练 3-中级教练 4-高级教练")
    private Integer level;
}
