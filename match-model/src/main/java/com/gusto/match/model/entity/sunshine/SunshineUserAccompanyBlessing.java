package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户祝福表
 *
 * <AUTHOR>
 * @since 2022/8/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sunshine_user_accompany_blessing", autoResultMap = true)
public class SunshineUserAccompanyBlessing extends BaseEntity {
    @Schema(description = "祝福ID")
    @TableId(value = "bless_id", type = IdType.AUTO)
    private Long blessId;

    @Schema(description = "用户Id")
    private Long userId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑Id")
    private Long recordId;

    @Schema(description = "祝福")
    private String blessing;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "头像")
    private String avatar;
}
