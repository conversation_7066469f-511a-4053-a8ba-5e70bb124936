package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 分页获取消息列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMessageGetPageReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long roomId;

    @Schema(description = "进入房间的时间，用于获取此时间之前的消息列表，保证分页结果固定", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long enterTime;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
