package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.ClosureCertMessage;
import com.gusto.match.model.entity.training.CoachLevelCertMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/11/24
 */
@Data

public class CertMessageRsp implements Serializable {
    private static final long serialVersionUID = -3771707259385689062L;

    @Schema(description = "中文名字")
    private String chineseName;

    @Schema(description = "名字拼音")
    private String pinyinName;

    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "教练等级")
    private Integer level;

    @Schema(description = "结业证书信息")
    private ClosureCertMessage closureCertMessage;

    @Schema(description = "教练等级证书信息")
    private CoachLevelCertMessage coachLevelCertMessage;

    @Schema(description = "0-默认 1-只有等级证书 2-两个证书都有")
    private Integer state;
}
