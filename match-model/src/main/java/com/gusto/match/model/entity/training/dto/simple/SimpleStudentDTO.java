package com.gusto.match.model.entity.training.dto.simple;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 学员
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class SimpleStudentDTO {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "创建日期")
    private Long createTime;
}
