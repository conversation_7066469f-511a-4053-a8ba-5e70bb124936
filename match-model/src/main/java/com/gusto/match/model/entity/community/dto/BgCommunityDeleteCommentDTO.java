package com.gusto.match.model.entity.community.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 社区禁言
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityDeleteCommentDTO {

    @Schema(description = "评论ID")
    @TableId(type = IdType.AUTO)
    private Long commentId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "动态作者类型：0-默认 1-用户 2-官方")
    private Integer postAuthorType;

    @Schema(description = "动态作者ID")
    private Long postAuthorId;

    @Schema(description = "发表评论的作者类型：0-默认 1-用户 2-官方")
    private Integer fromAuthorType;

    @Schema(description = "发表评论的作者ID")
    private Long fromAuthorId;

    @Schema(description = "发表评论的作者昵称")
    private String fromNickname;

    @Schema(description = "发表评论的作者头像")
    private String fromAvatar;

    @Schema(description = "评论对象的作者类型：0-默认 1-用户 2-官方")
    private Integer toAuthorType;

    @Schema(description = "评论对象的作者ID")
    private Long toAuthorId;

    @Schema(description = "评论对象的作者昵称")
    private String toNickname;

    @Schema(description = "评论对象的作者头像")
    private String toAvatar;

    @Schema(description = "根评论ID")
    private Long rootCommentId;

    @Schema(description = "回复评论ID")
    private Long toCommentId;

    @Schema(description = "内容，限制300字")
    private String content;

    @Schema(description = "摘要，用于显示在回复和评论列表页")
    private String summary;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "IP")
    private String ip;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "IP市")
    private String ipCity;

    @Schema(description = "评论时间")
    private Long commentTime;

    @Schema(description = "是否有楼中楼")
    private Boolean hasFloor;

    @Schema(description = "状态：0-默认 1-展示 2-用户删除 3-管理员删除 4-限流 5-审核不通过")
    private Integer state;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作时间")
    private Long operatorTime;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer operatorSource;

    @Schema(description = "动态摘要")
    private String postSummary;

    @Schema(description = "话题名称")
    private String topicName;

    @Schema(description = "文本审核请求ID")
    private String textReviewRequestId;

    @Schema(description = "文本审核结果：0-默认 1-未检测到风险 2-低风险 3-中风险 4-高风险 5-检测失败")
    private Integer textReviewResult;

    @Schema(description = "审核不通过原因")
    private String rejectReason;

}
