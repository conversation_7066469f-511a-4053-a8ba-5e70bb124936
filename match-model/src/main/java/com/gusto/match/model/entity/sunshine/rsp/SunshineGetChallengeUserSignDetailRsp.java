package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取挑战赛用户报名详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeUserSignDetailRsp {
    @Schema(description = "报名状态：0-默认 1-未报名 2-报名未支付 3-报名已支付")
    private Integer signState;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "挑战赛名称")
    private String matchName;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "报名套餐名称")
    private String signPackageName;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    private Long endTime;

    @Schema(description = "目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

    @Schema(description = "总陪跑人数")
    private Integer totalAccompanyMemberCount;

    @Schema(description = "总祝福人数")
    private Integer totalBlessingCount;

    @Schema(description = "完赛状态：0-默认 1-挑战中 2-挑战成功 3-挑战失败 4-挑战未开始")
    private Integer finishedState;

    @Schema(description = "完赛时间，单位毫秒")
    private Long finishedTime;

    @Schema(description = "完赛用时，单位秒")
    private Long duration;

    @Schema(description = "套餐图片")
    private String signPackageImg;

    @Schema(description = "陪跑ID 如果用户没有进行的陪跑 返回0")
    private Long recordId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "监护人")
    private String guardiansName;
}
