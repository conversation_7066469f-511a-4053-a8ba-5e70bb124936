package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取完赛证书渲染数据
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeTemplateGetCertReq {

    @Schema(description = "赛季ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seasonId;

    @Schema(description = "成绩ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long resultId;

}
