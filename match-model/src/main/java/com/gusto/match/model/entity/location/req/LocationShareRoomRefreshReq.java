package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 刷新房间获取共享定位和消息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareRoomRefreshReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long roomId;

    @Schema(description = "进入房间的时间，用于获取此时间之后的消息列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long enterTime;

}
