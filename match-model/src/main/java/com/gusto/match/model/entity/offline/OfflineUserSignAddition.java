package com.gusto.match.model.entity.offline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 线下赛用户报名附加信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
public class OfflineUserSignAddition implements Serializable {

    private static final long serialVersionUID = 5881463795924406589L;

    @Schema(description = "附加信息类型：0-默认 1-图片 2-文字 3-选项 4-用时 5-视频 6-历史成绩")
    private Integer additionType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "内容，如果是图片列表，图片链接用英文逗号连接。" +
            "如果是历史成绩跳过传‘｜’，不跳过则传‘id,赛事名称,项目,时间,成绩,图片,类型’，用‘|’分割，其中类型1为线下赛证书，2为其他证书，3为资格赛证书，此处不考虑新增其他证书")
    private String content;

}
