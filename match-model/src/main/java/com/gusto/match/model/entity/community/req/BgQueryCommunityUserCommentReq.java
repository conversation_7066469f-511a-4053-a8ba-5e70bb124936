package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.PositiveOrZero;

/**
 * 分页获取用户评论列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
public class BgQueryCommunityUserCommentReq {

    @Schema(description = "话题ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Long topicId;

    @Schema(description = "动态ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long postId = 0L;

    @Schema(description = "动态标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String postTitle = "";

    @Schema(description = "评论内容")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String commentContent = "";

    @Schema(description = "用户昵称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String fromNickname = "";

    @Schema(description = "用户手机")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String fromMobile = "";

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long fromAuthorId = 0L;

    @Schema(description = "排序类型：0-默认 1-最新 2-点赞最多")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sortType = 0;

    @Schema(description = "current")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long current = 1L;

    @Schema(description = "size")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long size = 20L;

}
