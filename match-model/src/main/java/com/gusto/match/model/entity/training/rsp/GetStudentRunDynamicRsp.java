package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取学员跑步动态响应
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class GetStudentRunDynamicRsp {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "距离，单位米")
    private Double distance;

    @Schema(description = "时间文本")
    private String runTimeTxt;

    @Schema(description = "runId")
    private Long runId;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer sportType;

    @Schema(description = "数据来源：1-app跑步 2-手动录入 3-手表 4-手环 5-跑步机")
    private Integer source;

    @Schema(description = "心率")
    private Integer heartRate;
}
