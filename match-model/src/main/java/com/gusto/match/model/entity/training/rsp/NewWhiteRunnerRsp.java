package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/3
 */
@Data

public class NewWhiteRunnerRsp {
    @Schema(description = "新增小白数量")
    private Integer newWhiteRunner;

    @Schema(description = "公司人员新增小白数量")
    private Integer companyNewWhiteRunner;

    @Schema(description = "外部人员新增小白详情")
    private List<NewWhiteRunnerInfo> newWhiteRunnerInfoList;

    @Schema(description = "公司内部新增小白详情")
    private List<NewWhiteRunnerInfo> companyNewWhiteRunnerInfoList;
}
