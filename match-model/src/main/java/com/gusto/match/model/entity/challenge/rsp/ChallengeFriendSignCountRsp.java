package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛已报名好友的数量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class ChallengeFriendSignCountRsp {

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "头像列表")
    private List<String> avatarList;

    @Schema(description = "报名数量")
    private Integer signCount;

}
