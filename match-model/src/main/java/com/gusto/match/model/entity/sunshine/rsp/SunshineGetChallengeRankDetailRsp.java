package com.gusto.match.model.entity.sunshine.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.match.model.entity.online.rsp.GetOnlineRankListElementRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛排行榜详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeRankDetailRsp {

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "赛事名称")
    private String matchName;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "榜单名称")
    private String rankName;

    @Schema(description = "本人排名")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GetOnlineRankListElementRsp currentRank;

    @Schema(description = "排名列表")
    private List<GetOnlineRankListElementRsp> rankList;
}
