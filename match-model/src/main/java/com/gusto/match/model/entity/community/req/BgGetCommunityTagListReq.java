package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 获取标签列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgGetCommunityTagListReq {

    @Schema(description = "话题ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long topicId = 0L;

}
