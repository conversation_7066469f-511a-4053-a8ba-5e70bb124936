package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.TrainingArticleDTO;
import com.gusto.match.model.entity.training.dto.TrainingVideoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取首页训练知识列表响应
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class GetHomeTrainingKnowledgeListRsp {
    @Schema(description = "文章列表")
    private List<TrainingArticleDTO> articleList;

    @Schema(description = "视频列表")
    private List<TrainingVideoDTO> videoList;

    @Schema(description = "评测列表")
    private List<TrainingArticleDTO> reviewList;
}
