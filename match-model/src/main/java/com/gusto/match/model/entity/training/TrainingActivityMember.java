package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import com.gusto.match.model.entity.training.req.SignInfoReq;
import com.gusto.match.model.mybatis.ListSignInfoReqJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 训练活动成员
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach_training_activity_member", autoResultMap = true)
public class TrainingActivityMember extends BaseEntity implements Deletable {
    private static final long serialVersionUID = 4462579317686930398L;

    @TableId(value = "member_id", type = IdType.AUTO)
    @Schema(description = "活动成员ID")
    private Long memberId;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "成员用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String nickname;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "报名填写要求列表")
    @TableField(typeHandler = ListSignInfoReqJsonTypeHandler.class)
    private List<SignInfoReq> signInfoList;

    @Schema(description = "状态 0-默认 1-未签到 2-签到")
    private Integer state;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "教练用户ID")
    private Long coachUserId;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Schema(description = "目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

    // TrainingActivityMemberTrainingState
    @Schema(description = "训练状态：0-默认 1-已完成")
    private Integer trainingState;

    @Schema(description = "是否已弹窗")
    private Boolean popUpWindow;

    @Schema(description = "活动开始时间")
    private Instant activityStartTime;

    @Schema(description = "活动结束时间")
    private Instant activityEndTime;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer sportType;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
