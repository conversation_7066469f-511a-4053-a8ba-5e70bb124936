package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.TrainingActivityListItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取首页我的训练活动列表响应
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class GetHomeMyTrainingActivityListRsp {
    @Schema(description = "最近活动活动")
    private TrainingActivityListItemDTO activity;

    @Schema(description = "0-默认 1-没有活动 2-只有一个活动 3-多于1个活动")
    private Integer state;
}
