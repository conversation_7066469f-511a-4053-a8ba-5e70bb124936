package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 挑战赛赛季名称
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class ChallengeUserSignDTO {

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

}
