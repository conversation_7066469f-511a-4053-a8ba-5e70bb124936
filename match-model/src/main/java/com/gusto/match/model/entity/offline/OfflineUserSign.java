package com.gusto.match.model.entity.offline;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.entity.user.UserCard;
import com.gusto.match.model.kafka.message.OfflineUserSignMessage;
import com.gusto.match.model.kafka.message.OfflineUserSignMessageItem;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import com.gusto.match.model.mybatis.ListOfflineUserSignAdditionJsonTypeHandler;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 线下赛用户报名
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_sign", autoResultMap = true)
public class OfflineUserSign extends BaseTenantEntity {

    private static final long serialVersionUID = -4264909920579157777L;

    @Schema(description = "报名ID")
    @TableId(type = IdType.AUTO)
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "性别：0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "电话")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "尺寸")
    private String size;

    @Schema(description = "国家/地区")
    private String country;

    @Schema(description = "证件号码")
    private String cardNumber;

    @Schema(description = "证件类型：0-默认 1-身份证 2-军官证 3-护照 4-港澳通行证 5-台胞证")
    private Integer cardType;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @Schema(description = "紧急联系人电话")
    private String emergencyContactMobile;

    @Schema(description = "邀请码")
    private String inviteCode;

    @Schema(description = "邀请码ID")
    private Long codeId;

    @Schema(description = "邀请码类型：0-默认 1-折扣 2-实际支付 3-优惠金额")
    private Integer codeType;

    @Schema(description = "邀请码优惠金额")
    private BigDecimal codeAmount;

    @Schema(description = "支持跑团ID")
    private Long supportClubId;

    @Schema(description = "本人签名图片")
    private String ownSignImage;

    @Schema(description = "监护人签名图片")
    private String parentSignImage;

    @Schema(description = "报名状态：0-默认 1-未报名 2-待审核 3-未通过审核 4-已通过审核/待抽签 5-未中签 6-已中签 7-报名成功 8-已取消报名")
    private Integer signState;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "报名费")
    private BigDecimal signAmount;

    @Schema(description = "商品金额")
    private BigDecimal goodsAmount;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "订单ID列表")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> orderIdList;

    @Schema(description = "订单号列表（我们生成）")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> outTradeNoList;

    @Schema(description = "交易单号（支付平台返回）")
    private String tradeNo;

    @Schema(description = "支付状态：0-默认 1-未支付 2-已支付 3-已退款 4-已部分退款")
    private Integer payState;

    @Schema(description = "支付金额，单位元")
    private BigDecimal payAmount;

    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "支付时间，单位毫秒")
    private Instant payTime;

    @Schema(description = "附加信息列表")
    @TableField(typeHandler = ListOfflineUserSignAdditionJsonTypeHandler.class)
    private List<OfflineUserSignAddition> additionList;

    @Schema(description = "附加信息提交时间")
    private Instant additionSubmitTime;

    @Schema(description = "审核不通过原因")
    private String refuseReason;

    @Schema(description = "退赛退款状态：0-默认 1-已申请 2-已退款 3-退款中 4-退款失败")
    private Integer refundState;

    @Schema(description = "退赛申请时间")
    private Instant refundApplyTime;

    @Schema(description = "退赛申请来源：0-默认 1-用户 2-管理员")
    private Integer refundApplySource;

    @Schema(description = "退款账户名")
    private String refundName;

    @Schema(description = "退款账户号")
    private String refundAccount;

    @Schema(description = "退款时间")
    private Instant refundTime;

    @Schema(description = "血型：0-默认 1-A 2-B 3-O 4-AB 5-其他")
    private Integer bloodType;

    @Schema(description = "管理员备注")
    private String adminRemark;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "来源：0-默认 1-用户报名 2-后台导入")
    private Integer source;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "队伍ID")
    private Long teamId;

    @Schema(description = "转让唯一码")
    private String transferUniqueCode;

    @Schema(description = "转让验证码")
    private String transferVerifyCode;

    @Schema(description = "转让申请时间")
    private Instant transferApplyTime;

    @Schema(description = "转让成功时间")
    private Instant transferSuccessTime;

    @Schema(description = "转让申请IP")
    private String transferApplyIp;

    @Schema(description = "转让原报名ID，接受转让的有")
    private Long transferOriginalSignId;

    @Schema(description = "转让新报名ID，申请转让的有")
    private Long transferNewSignId;

    @Schema(description = "修改项目原报名ID，旧报名有")
    private Long changeOriginalSignId;

    @Schema(description = "修改项目新报名ID，新报名有")
    private Long changeNewSignId;

    @Schema(description = "新报名提交IP")
    private String changeNewSignSubmitIp;

    @Schema(description = "自选号码")
    private String selectNumber;

    @Schema(description = "报名成功弹窗")
    private Boolean signSuccessPopUp;

    @Schema(description = "来源渠道：0-默认 1-微信 2-APP 3-其他 4-小程序")
    private Integer sourceChannel;

    @Schema(description = "来源类型：0-直接报名 1-微信分享 2-APP分享 3-公众号 4-Banner 5-海报分享 6-弹窗 7-外部 8-私域 9-跑团 10-后台导入 11-推送")
    private Integer sourceType;

    @Schema(description = "来源ID")
    private String sourceId;

    @Schema(description = "报名自选号码")
    private Integer signSelectNumber;

    @Schema(description = "报名选号金额")
    private BigDecimal signSelectNumberAmount;

    @Schema(description = "报名锁定选号缓存键")
    private String signSelectNumberCacheKey;

    @Schema(description = "提交报名是否勾选选号服务")
    private Boolean signSelectNumberSubmit;

    @Schema(description = "金额类型：0-默认 1-人名币 2-美元")
    private Integer amountType;

    @Schema(description = "多人合并报名-主报名ID")
    private Long multiSignMainSignId;

    @Schema(description = "多人合并报名-index")
    private Integer multiSignIndex;

    /**
     * 填充报名卡信息
     */
    public void fillUserSignCard(UserCard card, Long userId) {
        this.userId = userId;
        username = card.getRealName();
        sex = card.getSex();
        email = card.getEmail();
        country = card.getCountry();
        emergencyContact = card.getEmergencyContact();
        emergencyContactMobile = card.getEmergencyContactMobile();
        cardNumber = card.getCardNumber().toUpperCase();
        cardType = card.getCardType();
        province = card.getProvince();
        city = card.getCity();
        area = card.getArea();
        address = card.getAddress();
        bloodType = card.getBloodType();
        birthday = card.getBirthday();
    }

    /**
     * 填充旧报名信息
     */
    public void fillOldUserSign(OfflineUserSign oldSign) {
        raceId = oldSign.raceId;
        userId = oldSign.userId;
        username = oldSign.username;
        sex = oldSign.sex;
        mobile = oldSign.mobile;
        email = oldSign.email;
        size = oldSign.size;
        country = oldSign.country;
        cardNumber = oldSign.cardNumber;
        cardType = oldSign.cardType;
        province = oldSign.province;
        city = oldSign.city;
        area = oldSign.area;
        address = oldSign.address;
        emergencyContact = oldSign.emergencyContact;
        emergencyContactMobile = oldSign.emergencyContactMobile;
        ownSignImage = oldSign.ownSignImage;
        parentSignImage = oldSign.parentSignImage;
        bloodType = oldSign.bloodType;
        birthday = oldSign.birthday;
    }

    /**
     * 转换成Kafka消息子项
     */
    private OfflineUserSignMessageItem buildMessageItem(Integer signCount) {
        var item = new OfflineUserSignMessageItem();
        item.setProjectId(projectId);
        item.setSignId(signId);
        item.setSignCount(signCount);
        return item;
    }

    /**
     * 转换成Kafka消息
     */
    public OfflineUserSignMessage buildMessage(Integer signCount) {
        var message = new OfflineUserSignMessage();
        message.setSign(this.buildMessageItem(signCount));
        message.setCreateTime(Instant.now().toEpochMilli());
        return message;
    }

    /**
     * 转换成Kafka消息
     */
    public OfflineUserSignMessage buildMessageForRefund(List<Long> orderIdList, Integer signCount) {
        var message = new OfflineUserSignMessage();
        message.setSign(this.buildMessageItem(signCount));
        message.setCreateTime(Instant.now().toEpochMilli());
        message.setQuit(false);
        message.setRefundOrderIdList(orderIdList);
        return message;
    }

    /**
     * 支付成功
     */
    public void updateSignStateForPaySuccess(Integer signType, OfflineProject project) {
        // 报名状态：0-默认 1-未报名 2-待审核 3-未通过审核 4-已通过审核/待抽签 5-未中签 6-已中签 7-报名成功 8-已取消报名
        // 报名类型：0-默认 1-先支付后审核不抽签 2-先审核后支付不抽签 3-先支付后审核抽签 4-先审核抽签后支付
        if (signState == 4) {
            if (signType != 3) {
                // 报名成功
                signState = 7;
            }
        }
        // 其他状态照旧
//        AtomicBoolean haveSkip = new AtomicBoolean(false);
//        var projectAdditionMap = project.getAdditionList().stream()
//                .collect(Collectors.toMap(it -> it.getAdditionType() + "-" + it.getName(), it -> it));
//        var needReviewAdditionList = additionList.stream()
//                .filter(it -> {
//                    var key = it.getAdditionType() + "-" + it.getName();
//                    var currentAddition = projectAdditionMap.get(key);
//                    if (currentAddition != null) {
//                        if (currentAddition.getAdditionType() == 6 && it.getContent().equals("|")) {
//                            haveSkip.set(true);
//                        }
//                        if (currentAddition.getNeedReview() != null) {
//                            return currentAddition.getNeedReview();
//                        } else {
//                            return true;
//                        }
//                    } else {
//                        return true;
//                    }
//                })
//                .collect(Collectors.toList());
//        if (!needReviewAdditionList.isEmpty()) {
//            // 有需要审核的资料
//            if (signType == 2 || signType == 4) {
//                // 先审核后支付不抽签，先审核抽签后支付
//                // 报名成功
//                signState = 7;
//            } else {
//                // 先支付后审核不抽签，先支付后审核抽签
//                // 待审核
//                signState = 2;
//            }
//            if (haveSkip.get()) {
//                // 有跳过，审核不通过
//                signState = 3;
//            }
//        } else {
//            // 没有需要审核的资料
//            if (signType == 3) {
//                // 先支付后审核抽签
//                // 已通过审核/待抽签
//                signState = 4;
//            } else {
//                // 先支付后审核不抽签，先审核后支付不抽签，先审核抽签后支付
//                // 报名成功
//                signState = 7;
//            }
//        }
    }

    public String buildCacheKey() {
        return raceId + "-" + userId;
    }

    /**
     * 计算邀请码优惠 注意：调用此方法前需保证报名费没计入公益报名费
     *
     * @param discount
     */
    public void calCodeAmount(BigDecimal discount) {
        switch (codeType) {
            // 折扣
            case 1:
                codeAmount = signAmount.multiply(BigDecimal.ONE.subtract(discount));
                break;
            // 实际支付
            case 2:
                if (signAmount.compareTo(discount) <= 0) {
                    codeAmount = BigDecimal.ZERO;
                } else {
                    codeAmount = signAmount.subtract(discount);
                }
                break;
            // 优惠金额
            case 3:
                codeAmount = discount;
                break;
            default:
                codeAmount = BigDecimal.ZERO;
        }
    }

    /**
     * 获取退赛状态 0-默认 1-申请中 2-已退赛
     */
    public Integer buildQuitState() {
        var quitState = 0;
        // 申请过退赛
        if (refundApplySource > 0 && refundApplyTime != null) {
            quitState = 1;
        }
        // 修改过项目
        if (changeNewSignId > 0) {
            quitState = 1;
        }
        // 转让过名额
        if (StrUtil.isNotEmpty(transferUniqueCode)) {
            quitState = 1;
        }
        if (signState == 8) {
            quitState = 2;
        }
        return quitState;
    }

    public boolean isSignSuccess() {
        return payState != 1 && (signState >= 2 && signState <= 7);
    }

    public Boolean checkSelectNumber() {
        return StrUtil.isNumeric(selectNumber) || (signSelectNumber != null && signSelectNumber > 0);
    }

    public BigDecimal buildTotalAmount() {
        var totalAmount = BigDecimal.ZERO;
        if (signAmount != null) {
            totalAmount = totalAmount.add(signAmount);
        }
        if (goodsAmount != null) {
            totalAmount = totalAmount.add(goodsAmount);
        }
        if (signSelectNumberAmount != null) {
            totalAmount = totalAmount.add(signSelectNumberAmount);
        }
        if (codeAmount != null) {
            totalAmount = totalAmount.subtract(codeAmount);
        }
        return totalAmount;
    }

    public void releaseSignSelectNumber(List<Long> removeOrderIdList, Boolean refund) {
        signSelectNumber = 0;
        signSelectNumberCacheKey = "";
        if (refund) {
            if (orderIdList != null && !orderIdList.isEmpty() && !removeOrderIdList.isEmpty()) {
                orderIdList.removeAll(removeOrderIdList);
            }
        } else {
            signSelectNumberAmount = BigDecimal.ZERO;
            if (orderIdList != null && !orderIdList.isEmpty() && !removeOrderIdList.isEmpty()) {
                orderIdList.removeAll(removeOrderIdList);
            }
            totalAmount = buildTotalAmount();
        }

    }

}
