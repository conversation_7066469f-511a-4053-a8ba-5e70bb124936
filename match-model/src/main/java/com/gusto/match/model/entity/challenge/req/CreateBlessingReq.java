package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Data
public class CreateBlessingReq {

    @Schema(description = "陪跑ID")
    @Positive
    private Long recordId;

    @Schema(description = "祝福")
    @NotEmpty
    private String blessing;

}
