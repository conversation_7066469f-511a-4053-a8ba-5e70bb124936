package com.gusto.match.model.entity.sunshine.dto;

import com.gusto.match.model.entity.shop.dto.ShopGoodsDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 项目绑定套餐
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineProjectPackageDTO implements Serializable {

    private static final long serialVersionUID = -7603945636840611151L;

    @Schema(description = "套餐名称")
    private String name;

    @Schema(description = "排序，数值越大越靠前")
    private Integer sort;

    @Schema(description = "所有商品的销售总价")
    private BigDecimal saleAmount;

    @Schema(description = "第一个商品图片")
    private String image;

    @Schema(description = "商品列表，如果长度为0，直接拉起支付")
    private List<ShopGoodsDTO> goodsList;

}
