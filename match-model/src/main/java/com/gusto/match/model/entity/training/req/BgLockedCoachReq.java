package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Data

public class BgLockedCoachReq {
    @Schema(description = "教练Ids", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> coachIds;

    @Schema(description = "锁定 true-锁定 false-解锁", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean locked;
}
