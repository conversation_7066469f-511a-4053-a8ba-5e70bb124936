package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区标签
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_tag")
public class CommunityTag extends BaseEntity {

    private static final long serialVersionUID = 3389770163642936617L;

    @Schema(description = "标签ID")
    @TableId(type = IdType.AUTO)
    private Long tagId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布")
    private Integer publishSetting;

}
