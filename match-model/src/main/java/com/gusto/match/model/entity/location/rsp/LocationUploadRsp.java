package com.gusto.match.model.entity.location.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 * 上传定位
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@AllArgsConstructor
public class LocationUploadRsp {

    @Schema(description = "上传结果")
    private Boolean uploadResult;

    @Schema(description = "失败类型：0-默认 1-无上传权限")
    private Integer failType;

}
