package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/1
 */
@Data
public class GetBlessingElementDetailRsp {
    @Schema(description = "祝福ID")
    private Long blessId;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "祝福")
    private String blessing;

    @Schema(description = "陪跑Id")
    private Long recordId;
}
