package com.gusto.match.model.entity.community.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 社区禁言
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityDeletePostDTO {

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "作者类型：0-默认 1-用户 2-官方")
    private Integer authorType;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "标题，限制20字")
    private String title;

    @Schema(description = "内容，限制1000字")
    private String content;

    @Schema(description = "图片列表，限制9张")
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "分享量")
    private Integer shareCount;

    @Schema(description = "IP")
    private String ip;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "IP市")
    private String ipCity;

    @Schema(description = "发布时间")
    private Long postTime;

    @Schema(description = "状态：0-默认 1-展示 2-用户删除 3-管理员删除 4-限流 5-审核不通过")
    private Integer state;

    @Schema(description = "话题名称")
    private String topicName;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作时间")
    private Long operatorTime;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer operatorSource;

    @Schema(description = "图片审核请求ID")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> imageReviewRequestId;

    @Schema(description = "图片审核结果：0-默认 1-未检测到风险 2-低风险 3-中风险 4-高风险 5-检测失败")
    private Integer imageReviewResult;

    @Schema(description = "文本审核请求ID")
    private String textReviewRequestId;

    @Schema(description = "文本审核结果：0-默认 1-未检测到风险 2-低风险 3-中风险 4-高风险 5-检测失败")
    private Integer textReviewResult;

    @Schema(description = "审核不通过原因")
    private String rejectReason;

}
