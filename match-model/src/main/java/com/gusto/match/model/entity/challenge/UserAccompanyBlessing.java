package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 用户祝福表
 *
 * <AUTHOR>
 * @since 2022/8/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "match_user_accompany_blessing", autoResultMap = true)
public class UserAccompanyBlessing extends BaseEntity {

    private static final long serialVersionUID = -539993484969837432L;

    @Schema(description = "祝福ID")
    @TableId(value = "bless_id", type = IdType.AUTO)
    private Long blessId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "祝福")
    private String blessing;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "挑战者回复")
    private String reply;

    @Schema(description = "回复时间")
    private Instant replyTime;

    @Schema(description = "是否有回复")
    private Boolean hasReply;

    @Schema(description = "回复是否已读")
    private Boolean replyRead;

}
