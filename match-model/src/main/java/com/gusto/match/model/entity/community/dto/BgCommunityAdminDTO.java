package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
public class BgCommunityAdminDTO {

    @Schema(description = "管理员ID")
    private Long adminId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "话题名称")
    private String topicName;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主")
    private Integer adminType;

    @Schema(description = "创建管理员ID")
    private Long createAdminId;

    @Schema(description = "创建管理员类型：0-默认 1-前台 2-后台")
    private Integer createAdminType;

}
