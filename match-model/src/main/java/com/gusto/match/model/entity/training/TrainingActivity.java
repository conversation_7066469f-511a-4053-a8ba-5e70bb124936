package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 训练活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach_training_activity", autoResultMap = true)
public class TrainingActivity extends BaseEntity implements Deletable {
    private static final long serialVersionUID = 7026172392880381991L;

    @TableId(value = "activity_id", type = IdType.AUTO)
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "创建教练ID")
    private Long coachId;

    @Schema(description = "创建教练用户ID")
    private Long coachUserId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;

    @Schema(description = "已报名人数")
    private Integer signCount;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Schema(description = "总距离")
    private Double totalDistance;

    @Schema(description = "教练等级")
    private Integer level;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
