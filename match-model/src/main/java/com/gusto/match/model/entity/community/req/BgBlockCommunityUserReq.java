package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 禁言
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgBlockCommunityUserReq {

    @Schema(description = "对象类型：0-默认 1-动态 2-评论 3-用户", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer objectType;

    @Schema(description = "对象ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long objectId;

    @Schema(description = "时长类型：0-默认 1-1天 2-7天 3-30天 4-永久", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer durationType;

    @Schema(description = "原因类型：0-默认 1-涉黄信息 2-有害信息 3-人身攻击 4-违法信息 5-不实信息 6-违规营销 7-其他", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer reasonType;

    @Schema(description = "原因内容")
    private String reasonContent = "";

}
