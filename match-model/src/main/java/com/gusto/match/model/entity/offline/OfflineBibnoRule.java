package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛号码规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_bibno_rule")
public class OfflineBibnoRule extends BaseTenantEntity {

    private static final long serialVersionUID = 2341743740834803919L;

    @Schema(description = "规则ID")
    @TableId(type = IdType.AUTO)
    private Long ruleId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "区分性别：0-默认 1-区分 2-不区分，大于0时不可修改")
    private Integer bibnoDivideSex;

    @Schema(description = "男号码长度，大于0时不可修改")
    private Integer bibnoManLength;

    @Schema(description = "男号码前缀，大于0时不可修改")
    private String bibnoManSub;

    @Schema(description = "男号码起始数字")
    private Integer bibnoManStart;

    @Schema(description = "男号码起始数字")
    private Integer bibnoManEnd;

    @Schema(description = "女号码长度，大于0时不可修改")
    private Integer bibnoWomanLength;

    @Schema(description = "女号码前缀，大于0时不可修改")
    private String bibnoWomanSub;

    @Schema(description = "女号码起始数字")
    private Integer bibnoWomanStart;

    @Schema(description = "女号码起始数字")
    private Integer bibnoWomanEnd;

}
