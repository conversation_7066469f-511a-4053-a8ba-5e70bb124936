package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取挑战赛已报名好友列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class ChallengeGetFriendSignListItemRsp {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "支付时间")
    private Long payTime;

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "完赛状态：0-默认 1-未挑战 2-挑战成功 3-挑战失败")
    private Integer finishedState;

    @Schema(description = "是否报名")
    private Boolean sign;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "是否展示")
    private Boolean show;

}
