package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 后台训练文章
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class BgTrainingArticleDTO {

    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章分类：0-默认 1-文章知识 2-装备测评")
    private Integer type;

    @Schema(description = "文章链接")
    private String url;

    @Schema(description = "发布时间，时间戳，单位毫秒")
    private Long postTime;

    @Schema(description = "浏览量")
    private Long view;

    @Schema(description = "分享次数")
    private Long shareCount;

    @Schema(description = "是否置顶，false=不置顶，true=置顶")
    private Boolean top;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;
}
