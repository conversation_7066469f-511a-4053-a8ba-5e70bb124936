package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/8
 */
@Data

public class BgFullCoachApplication {

    @Schema(description = "教练申请记录ID")
    private Long recordId;

    @Schema(description = "开始跑步时间 (单位月)")
    private Long runAge;

    @Schema(description = "全马PB成绩，单位秒")
    private Long marathonPb;

    @Schema(description = "半马PB成绩，单位秒")
    private Long halfMarathonPb;

    @Schema(description = "培训经历")
    private String trainingExperience;

    @Schema(description = "教学经历")
    private String teachingExperience;
}
