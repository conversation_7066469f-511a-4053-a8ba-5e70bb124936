package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 用户陪跑记录
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
public class UserAccompanyRunDTO {

    @Schema(description = "记录ID，更新时必填")
    private Long recordId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "报名ID，创建时必填")
    private Long signId;

    @Schema(description = "活动封面", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coverImage;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String activityName;

    @Schema(description = "活动地点坐标", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coordinate;

    @Schema(description = "活动地点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String location;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "截止报名时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long deadLineSignTime;

    @Schema(description = "人数限制：0为不限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer numberLimit = 0;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "活动详情图片列表")
    private List<String> activityImageList;

    @Schema(description = "前五名陪跑成员的头像列表")
    private List<String> memberAvatarList = Collections.emptyList();

}
