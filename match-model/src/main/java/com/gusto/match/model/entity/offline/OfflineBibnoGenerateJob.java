package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛号码生成任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_bibno_generate_job", autoResultMap = true)
public class OfflineBibnoGenerateJob extends BaseTenantEntity {

    private static final long serialVersionUID = -4207367325623041997L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "号码规则ID")
    private Long ruleId;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "应生成总数")
    private Integer totalCount;

    @Schema(description = "成功生成的数量")
    private Integer successCount;

}
