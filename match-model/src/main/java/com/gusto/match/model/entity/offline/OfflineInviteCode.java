package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <p>
 * 线下赛邀请码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_invite_code")
public class OfflineInviteCode extends BaseTenantEntity {

    private static final long serialVersionUID = 7104989342388754325L;

    @Schema(description = "邀请码ID")
    @TableId(type = IdType.AUTO)
    private Long codeId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "邀请码类型：0-默认 1-折扣 2-实际支付 3-优惠金额")
    private Integer codeType;

    @Schema(description = "邀请码")
    private String code;

    @Schema(description = "优惠金额")
    private BigDecimal discount;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "是否受报名名额限制")
    private Boolean signLimit;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户ID")
    private Long signId;

    @Schema(description = "使用状态：0-默认 1-未使用 2-已使用 3-已过期")
    private Integer useState;

    @Schema(description = "赛事类型限制：0-不限制 1-马拉松 2-越野赛 3-铁人三项 4-徒步 5-欢乐跑 6-骑行")
    private Integer raceTypeLimit;

    @Schema(description = "证件号码")
    private String cardNumber;

    @Schema(description = "生成类型：0-默认 1-普通 2-全局")
    private Integer genType;

}
