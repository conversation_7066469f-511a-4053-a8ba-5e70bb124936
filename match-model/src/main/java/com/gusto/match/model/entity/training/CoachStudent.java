package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 教练-学员关系
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coach_student_relation")
public class CoachStudent extends BaseEntity implements Deletable {
    private static final long serialVersionUID = 1946406447004649094L;

    @TableId(value = "relation_id", type = IdType.AUTO)
    @Schema(description = "学员ID")
    private Long studentId;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "教练用户ID")
    private Long coachUserId;

    @Schema(description = "学员用户ID")
    private Long studentUserId;

    @Schema(description = "学员昵称")
    private String studentNickname;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Schema(description = "学员是否已弹窗")
    private Boolean popUpWindowCoach;

    @Schema(description = "教练是否显示弹窗")
    private Boolean popUpWindowStudent;

    @Schema(description = "来源：0-默认 1-APP 2-H5")
    private Integer source;

    @Schema(description = "最近一次跑步时间")
    private Instant latestRunTime;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "最近一次跑步距离")
    private Double latestRunDistance;

    @Schema(description = "最近一次跑步的ID")
    private Long latestRunRecordId;

    // 3.5.0
    @Schema(description = "跑步动态是否已读")
    private Boolean runDynamic;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
