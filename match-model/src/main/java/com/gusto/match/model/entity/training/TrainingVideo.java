package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 训练视频
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach_training_video")
public class TrainingVideo extends BaseEntity implements Deletable {
    private static final long serialVersionUID = -1575336649527996690L;

    @TableId(value = "video_id", type = IdType.AUTO)
    @Schema(description = "视频ID")
    private Long videoId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "视频链接")
    private String url;

    @Schema(description = "时长，单位秒")
    private Long duration;

    @Schema(description = "真实浏览量")
    private Long view;

    @Schema(description = "虚拟浏览量，选填")
    private Long fakeView;

    @Schema(description = "播完次数")
    private Long playCount;

    @Schema(description = "分享次数")
    private Long shareCount;

    @Schema(description = "发布时间")
    private Instant postTime;

    @Schema(description = "置顶，false=不置顶，true=置顶")
    private Boolean top;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
