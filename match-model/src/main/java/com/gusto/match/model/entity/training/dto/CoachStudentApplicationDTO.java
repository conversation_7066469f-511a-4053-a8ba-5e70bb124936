package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 新学员申请记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class CoachStudentApplicationDTO {
    @Schema(description = "新学员申请记录ID")
    private Long recordId;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "教练用户ID")
    private Long coachUserId;

    @Schema(description = "学员用户ID")
    private Long studentUserId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "申请原因")
    private String reason;

    @Schema(description = "审核状态：0-全部 1-待审核 2-通过 3-拒绝")
    private Integer state;

    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Schema(description = "是否已读状态")
    private Boolean readState;

    @Schema(description = "备注")
    private String note;
}
