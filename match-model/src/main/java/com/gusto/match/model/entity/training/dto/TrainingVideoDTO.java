package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练视频
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class TrainingVideoDTO {
    @Schema(description = "视频ID")
    private Long videoId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "时长，单位秒")
    private Integer duration;

    @Schema(description = "发布时间")
    private Long postTime;

    @Schema(description = "浏览次数")
    private Long look;

    @Schema(description = "分享次数")
    private Long shareCount;
}
