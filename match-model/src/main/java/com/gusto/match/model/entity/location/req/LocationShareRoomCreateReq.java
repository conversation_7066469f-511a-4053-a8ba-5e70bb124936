package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;

/**
 * <p>
 * 创建房间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareRoomCreateReq {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 30)
    private String name;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long endTime;

    @Schema(description = "围观人数限制，为0表示无限制", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Integer memberLimit;

    @Schema(description = "共享人数限制，为0表示无限制", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Integer shareLimit;

    @Schema(description = "公开状态：0-默认 1-公开 2-不公开", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer publicState;

    @Schema(description = "轨迹名称")
    private String trackName = "";

    @Schema(description = "轨迹链接")
    private String trackUrl = "";

    @Schema(description = "轨迹起点")
    private String trackStart = "";

    @Schema(description = "轨迹终点")
    private String trackEnd = "";

    @Schema(description = "轨迹距离，单位米")
    private Double trackDistance = 0.0;

    @Schema(description = "轨迹点位数")
    private Integer trackPointCount = 0;

}
