package com.gusto.match.model.entity.location.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 上传定位
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LocationUploadReq {

    @Schema(description = "纬度")
    private Double latitude = 0.0;

    @Schema(description = "经度")
    private Double longitude = 0.0;

    @Schema(description = "运动时间，单位秒")
    private Long duration = 0L;

    @Schema(description = "运动距离，单位米")
    private Double distance = 0.0;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace = 0.0;

    @Schema(description = "实时配速，单位秒")
    private Double currentPace = 0.0;

    @Schema(description = "平均步频，单位步/分钟")
    private Integer averageStepRate = 0;

    @Schema(description = "平均步幅，单位厘米")
    private Double averageStride = 0.0;

    @Schema(description = "累计上升，单位米")
    private Double totalAscent = 0.0;

    @Schema(description = "方向")
    private Double direction = 0.0;

    @Schema(description = "是否运动中")
    private Boolean moving = false;

    @Schema(description = "是否展示方向")
    private Boolean showDirection = false;

}
