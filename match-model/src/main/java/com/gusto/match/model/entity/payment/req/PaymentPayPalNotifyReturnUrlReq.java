package com.gusto.match.model.entity.payment.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentPayPalNotifyReturnUrlReq {

    @Schema(description = "token")
    private String token;

    @Schema(description = "PayerID")
    private String payerID;

}
