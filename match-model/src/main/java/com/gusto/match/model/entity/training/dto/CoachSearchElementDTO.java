package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Data

public class CoachSearchElementDTO {
    @Schema(description = "教练ID")
    private Integer coachId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "等级")
    private Integer level;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "是否被关注 0-默认 1-关注 2-没有关注")
    private Integer attention;

    @Schema(description = "粉丝")
    private Long fans;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;
}
