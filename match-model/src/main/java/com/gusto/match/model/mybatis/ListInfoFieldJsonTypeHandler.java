package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.survey.dto.InfoField;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/20
 */
public class ListInfoFieldJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<InfoField>> typeReference = new TypeReference<>() {
    };

    public ListInfoFieldJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
