package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

/**
 * 设置挑战时间和挑战地点
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
public class ChallengeUserSignSetTargetReq {

    @Schema(description = "赛季ID")
    @Positive
    private Long seasonId;

    @Schema(description = "挑战时间")
    @Positive
    private Long challengeTime;

    @Schema(description = "挑战地点坐标")
    @NotEmpty
    private String challengeCoordinate;

    @Schema(description = "挑战地点名称")
    @NotEmpty
    private String challengeLocation;

    @Schema(description = "挑战距离，单位米")
    @PositiveOrZero
    private Double challengeDistance;

}
