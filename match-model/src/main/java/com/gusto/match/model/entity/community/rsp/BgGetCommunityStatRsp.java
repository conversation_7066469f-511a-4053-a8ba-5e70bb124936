package com.gusto.match.model.entity.community.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取总统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class BgGetCommunityStatRsp {

    @Schema(description = "话题总数")
    private Long topicCount;

    @Schema(description = "话题总真实点击数")
    private Long topicRealClickCount;

    @Schema(description = "动态总数")
    private Long postCount;

    @Schema(description = "评论总数")
    private Long commentCount;

    @Schema(description = "上月话题总数")
    private Long lastMonthTopicCount;

    @Schema(description = "上月话题总真实点击数")
    private Long lastMonthTopicRealClickCount;

    @Schema(description = "上月动态总数")
    private Long lastMonthPostCount;

    @Schema(description = "上月评论总数")
    private Long lastMonthCommentCount;

}
