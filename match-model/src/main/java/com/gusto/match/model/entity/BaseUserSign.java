package com.gusto.match.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 基类-用户报名
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseUserSign extends BaseEntity {
    private static final long serialVersionUID = 3689800966772314983L;

    @Schema(description = "报名ID")
    @TableId(value = "sign_id", type = IdType.AUTO)
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "电话")
    private String mobile;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "订单ID列表")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> orderIds;

    @Schema(description = "订单号列表（我们生成）")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> outTradeNoList;

    @Schema(description = "交易单号（支付平台返回）")
    private String tradeNo;

    @Schema(description = "支付状态：0-默认 1-未支付 2-已支付 3-已退款 4-已部分退款")
    private Integer payState;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "支付时间")
    private Instant payTime;
}
