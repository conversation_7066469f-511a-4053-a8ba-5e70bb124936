package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * <p>
 * 分页获取收到的点赞列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class GetCommunityReceiveLikePageReq {

    @Parameter(description = "current")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long current = 1L;

    @Parameter(description = "size")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long size = 10L;

}
