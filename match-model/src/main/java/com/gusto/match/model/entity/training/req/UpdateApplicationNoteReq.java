package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设置学员申请备注请求
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data

public class UpdateApplicationNoteReq {
    @Schema(description = "申请记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long recordId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String note;
}
