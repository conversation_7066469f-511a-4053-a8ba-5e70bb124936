package com.gusto.match.model.entity.training;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/11/24
 */
@Data

public class ClosureCertMessage implements Serializable {
    private static final long serialVersionUID = -1490596372497889915L;

    @Schema(description = "万名教练计划举办时间")
    private String coachOfficialPlanTime;

    @Schema(description = "万名教练计划名字")
    private String coachOfficialPlanName;

    @Schema(description = "获得证书时间")
    private String getCertTime;
}
