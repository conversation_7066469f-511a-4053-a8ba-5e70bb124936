package com.gusto.match.model.entity.challenge.dto;

import com.gusto.match.model.entity.match.dto.MatchSeasonDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 后台-赛事详细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
public class BgMatchDTO {

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛事名称")
    private String title;

    @Schema(description = "状态：0-未上线 1-上线")
    private Integer state;

    @Schema(description = "赛事类型：0-普通 1-系列赛")
    private Integer matchType;

    @Schema(description = "排序号")
    private Long sort;

    @Schema(description = "当前赛季ID")
    private Long currentSeasonId;

    @Schema(description = "当前赛季")
    private MatchSeasonDTO currentSeason;

    @Schema(description = "赛季列表")
    private List<MatchSeasonDTO> seasonList = Collections.emptyList();

}
