package com.gusto.match.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 基类
 *
 * <AUTHOR>
 * @since 2021-06-18
 */
@Data
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 8723299312457459655L;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Instant createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updateTime;

}
