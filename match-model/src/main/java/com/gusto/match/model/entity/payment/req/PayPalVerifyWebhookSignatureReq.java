package com.gusto.match.model.entity.payment.req;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
public class PayPalVerifyWebhookSignatureReq {

    @Schema(description = "HTTP传输的ID包含在通知消息的PAYPAL-TRANSMISSION-ID标头中")
    @JSONField(name = "transmission_id", ordinal = 1)
    private String transmissionId;

    @Schema(description = "HTTP传输的日期和时间，采用Internet日期和时间格式显示在通知消息的PAYPAL-TRANSMISSION-TIME标头中")
    @JSONField(name = "transmission_time", ordinal = 2)
    private String transmissionTime;

    @Schema(description = "X.509公钥证书从此URL下载证书并使用它来验证签名从PAYPAL-CERT-URL响应标头中提取此值，该响应标头随Webhook通知一起接收")
    @JSONField(name = "cert_url", ordinal = 3)
    private String certUrl;

    @Schema(description = "PayPal用于生成签名的算法，您可以使用它来验证签名从PAYPAL-AUTH-ALGO响应标头中提取此值，该标头随Webhook通知一起接收")
    @JSONField(name = "auth_algo", ordinal = 4)
    private String authAlgo;

    @Schema(description = "PayPal生成的非对称签名显示在通知消息的PAYPAL-TRANSMISSION-SIG标头中")
    @JSONField(name = "transmission_sig", ordinal = 5)
    private String transmissionSig;

    @Schema(description = "在DeveloperPortal帐户中配置的Webhook的ID")
    @JSONField(name = "webhook_id", ordinal = 6)
    private String webhookId;

    @Schema(description = "Webhook事件通知")
    @JSONField(name = "webhook_event", ordinal = 7)
    private Object webhookEvent;

}
