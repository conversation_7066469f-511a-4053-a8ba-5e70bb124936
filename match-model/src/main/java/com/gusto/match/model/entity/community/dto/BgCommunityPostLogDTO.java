package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 社区动态日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityPostLogDTO {

    @Schema(description = "日志ID")
    private Long logId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "操作人类型：0-默认 1-用户 2-管理员 3-系统")
    private Integer operatorType;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "日志类型：0-默认 1-禁言 2-删除动态 3-删除评论 4-隐藏动态 5-隐藏评论")
    private Integer logType;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer source;

    @Schema(description = "创建时间")
    private Long createTime;

}
