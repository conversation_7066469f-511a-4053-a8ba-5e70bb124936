package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class CommunityReceiveCommentDTO {

    @Schema(description = "评论ID")
    private Long commentId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "发表评论的作者类型：0-默认 1-用户 2-官方")
    private Integer fromAuthorType;

    @Schema(description = "发表评论的作者ID")
    private Long fromAuthorId;

    @Schema(description = "发表评论的作者昵称")
    private String fromNickname;

    @Schema(description = "发表评论的作者头像")
    private String fromAvatar;

    @Schema(description = "内容，限制300字")
    private String content;

    @Schema(description = "摘要，用于显示在回复和评论列表页")
    private String summary;

    @Schema(description = "评论时间")
    private Long commentTime;

    @Schema(description = "动态封面")
    private String postCoverImage;

    @Schema(description = "回复评论ID，为0时表示评论动态，不为0时表示回复评论")
    private Long toCommentId;

    @Schema(description = "是否已点赞")
    private Boolean like;

}
