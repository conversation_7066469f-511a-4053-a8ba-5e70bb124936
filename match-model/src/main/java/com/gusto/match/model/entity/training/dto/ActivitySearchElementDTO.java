package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Data

public class ActivitySearchElementDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "已报名人数")
    private Integer signCount;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;
}
