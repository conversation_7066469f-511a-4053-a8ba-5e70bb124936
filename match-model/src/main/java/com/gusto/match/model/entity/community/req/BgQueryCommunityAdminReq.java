package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取管理员列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryCommunityAdminReq {

    @Schema(description = "话题ID")
    private Long topicId = 0L;

    @Schema(description = "昵称")
    private String nickname = "";

    @Schema(description = "手机")
    private String mobile = "";

    @Schema(description = "用户ID")
    private Long userId = 0L;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主")
    private Integer adminType = 0;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
