package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.FullTrainingActivityDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/15
 */
@Data

public class GetTrainingActivityInfoRsp {
    @Schema(description = "活动详情")
    private FullTrainingActivityDTO trainingActivityDTO;

    @Schema(description = "用户是否参加")
    private Boolean userJoin;

    @Schema(description = "用户是否已经签到 0-默认 1-未签到 2-签到")
    private Integer userSignInState;

    @Schema(description = "是否是本人")
    private Boolean myself;
}
