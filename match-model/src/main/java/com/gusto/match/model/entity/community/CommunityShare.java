package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区分享
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_share")
public class CommunityShare extends BaseEntity {

    private static final long serialVersionUID = 993359875533120726L;

    @Schema(description = "分享ID")
    @TableId(type = IdType.AUTO)
    private Long shareId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "动态作者类型：0-默认 1-用户 2-官方")
    private Integer postAuthorType;

}
