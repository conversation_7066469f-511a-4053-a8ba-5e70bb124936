package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.run.rsp.NewUserRunGetStat2023MarathonRsp;

import java.io.IOException;

/**
 * eg: <result column="size_list" property="sizeList" jdbcType="VARCHAR" typeHandler="com.gusto.match.model.mybatis
 * .ListStringJsonTypeHandler"/>
 * <p>
 * ps: 记得 @TableName(autoResultMap = true)
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public class Marathon2023JsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<NewUserRunGetStat2023MarathonRsp> typeReference = new TypeReference<>() {
    };

    public Marathon2023JsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
