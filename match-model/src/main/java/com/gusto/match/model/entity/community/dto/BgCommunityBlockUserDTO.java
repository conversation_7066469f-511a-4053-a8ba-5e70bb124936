package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 社区禁言
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityBlockUserDTO {

    @Schema(description = "禁言ID")
    private Long blockId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论 3-用户")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "时长类型：0-默认 1-1天 2-7天 3-30天 4-永久")
    private Integer durationType;

    @Schema(description = "原因类型：0-默认 1-涉黄信息 2-有害信息 3-人身攻击 4-违法信息 5-不实信息 6-违规营销 7-其他")
    private Integer reasonType;

    @Schema(description = "原因内容")
    private String reasonContent;

    @Schema(description = "到期时间")
    private Long expireTime;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer source;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "话题名称")
    private String topicName;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "用户手机")
    private String mobile;

    @Schema(description = "对象内容")
    private String objectContent;

    @Schema(description = "对象图片列表")
    private List<String> objectImageList;

}
