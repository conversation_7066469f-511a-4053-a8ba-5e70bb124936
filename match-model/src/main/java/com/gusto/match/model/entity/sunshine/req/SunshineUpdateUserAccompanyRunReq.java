package com.gusto.match.model.entity.sunshine.req;

import com.gusto.match.model.entity.match.req.CreateOrderReq;
import com.gusto.match.model.entity.sunshine.dto.SunshineUserAccompanyRunDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新陪跑请求
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Data

public class SunshineUpdateUserAccompanyRunReq {
    @Schema(description = "陪跑记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private SunshineUserAccompanyRunDTO record;

    @Schema(description = "创建订单请求")
    private CreateOrderReq createOrderReq;
}
