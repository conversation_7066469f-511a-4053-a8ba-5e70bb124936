package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取用户列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryCommunityUserReq {

    @Schema(description = "昵称")
    private String nickname = "";

    @Schema(description = "手机")
    private String mobile = "";

    @Schema(description = "用户ID")
    private Long userId = 0L;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
