package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取陪跑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
public class ChallengeUserSignGetMemberListRsp {

    @Schema(description = "累计陪跑报名总数")
    private Integer totalSignCount;

    @Schema(description = "累计陪跑到场人数")
    private Integer totalAttendCount;

    @Schema(description = "累计陪跑异地人数")
    private Integer totalRemoteCount;

    @Schema(description = "陪跑成员分组")
    private List<ChallengeUserSignGetMemberListGroupRsp> memberGroup;

    @Schema(description = "项目名称")
    private String projectName;

}
