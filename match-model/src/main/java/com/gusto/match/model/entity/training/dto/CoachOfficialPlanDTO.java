package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 万名教练计划
 *
 * <AUTHOR>
 * @since 2021/8/20
 */
@Data

public class CoachOfficialPlanDTO implements Serializable {
    private static final long serialVersionUID = -8546107346144448641L;

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "期号")
    private Long number;

    //V1.3
    @Schema(description = "课程开始时间")
    private Long classStartTime;

    @Schema(description = "课程结束时间")
    private Long classEndTime;
}
