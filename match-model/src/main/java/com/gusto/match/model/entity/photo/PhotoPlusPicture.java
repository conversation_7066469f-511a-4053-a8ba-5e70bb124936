package com.gusto.match.model.entity.photo;

import lombok.Data;

/**
 * PhotoPlus 图片信息
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class PhotoPlusPicture {
    
    /**
     * 大图链接
     */
    private String bigImg;
    
    /**
     * 图片ID
     */
    private Long picId;
    
    /**
     * 图片上传时间
     */
    private String createTime;
    
    /**
     * 图片大小 单位kb
     */
    private Long picSize;
    
    /**
     * 上传人名称
     */
    private String retoucher;
    
    /**
     * 图片宽度
     */
    private Integer width;
    
    /**
     * 原图链接
     */
    private String originImg;
    
    /**
     * 摄影师名称
     */
    private String camer;
    
    /**
     * 拍摄时间
     */
    private String relateTime;
    
    /**
     * 小图链接
     */
    private String smallImg;
    
    /**
     * 图片名称
     */
    private String picName;
    
    /**
     * 图片hash值
     */
    private String picHash;
    
    /**
     * 图片高度
     */
    private Integer height;

}
