package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台训练视频类目 DTO类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data

public class BgTrainingVideoCategoryDTO {

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "创建时间")
    private Long createTime;
}
