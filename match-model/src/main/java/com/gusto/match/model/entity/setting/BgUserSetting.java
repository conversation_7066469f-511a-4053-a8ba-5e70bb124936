package com.gusto.match.model.entity.setting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 后台用户设置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("new_bg_user_setting")
public class BgUserSetting extends BaseEntity {

    private static final long serialVersionUID = 3321839468888685742L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "设置ID")
    private Long settingId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "是否开启debug模式")
    private Boolean debug;

}
