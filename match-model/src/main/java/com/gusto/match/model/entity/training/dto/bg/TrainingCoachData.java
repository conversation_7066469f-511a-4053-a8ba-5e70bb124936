package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class TrainingCoachData {
    @Schema(description = "本月新增教练人数")
    private Long newCoachCount;

    @Schema(description = "完成半马人数（但没有申请成为教练）")
    private Long finishHalfMarathonCount;

    @Schema(description = "准教练人数")
    private Long associateCoachCount;

    @Schema(description = "正式教练人数")
    private Long officialCoachCount;

    @Schema(description = "总教练人数")
    private Long coachCount;

    @Schema(description = "新增学员人数")
    private Long newStudentCount;

    @Schema(description = "新学员注册人数")
    private Long newStudentRegisterCount;

    @Schema(description = "学员人数")
    private Long studentCount;

    @Schema(description = "新注册学员登录APP人数")
    private Long newStudentRegisterLoginCount;
}
