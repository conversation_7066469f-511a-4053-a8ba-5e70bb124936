package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 新学员申请记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coach_student_application")
public class StudentApplication extends BaseEntity implements Deletable {
    private static final long serialVersionUID = 5084787202978216560L;

    @TableId(value = "record_id", type = IdType.AUTO)
    @Schema(description = "新学员申请记录ID")
    private Long recordId;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "教练用户ID")
    private Long coachUserId;

    @Schema(description = "学员用户ID")
    private Long studentUserId;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "申请原因")
    private String reason;

    @Schema(description = "审核状态：0-全部 1-待审核 2-通过 3-拒绝")
    private Integer state;

    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Schema(description = "是否已读")
    private Boolean readState;

    // V3.2.0
    @Schema(description = "备注")
    private String note;

    @Schema(description = "来源：0-默认 1-APP 2-H5")
    private Integer source;

    @Override

    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
