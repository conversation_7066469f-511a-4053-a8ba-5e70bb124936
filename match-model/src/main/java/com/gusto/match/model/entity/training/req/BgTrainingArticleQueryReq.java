package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练文章后台查询类
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data

public class BgTrainingArticleQueryReq {

    @Schema(description = "字段ID，1=视频名称，2=视频ID")
    private Integer fieldId;

    @Schema(description = "字段值")
    private Object fieldValue;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;

    @Schema(description = "查询开始时间，时间戳，单位毫秒")
    private Long startTime;

    @Schema(description = "查询结束时间，时间戳，单位毫秒")
    private Long endTime;
}
