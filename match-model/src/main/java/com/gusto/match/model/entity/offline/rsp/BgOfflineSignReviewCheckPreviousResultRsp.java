package com.gusto.match.model.entity.offline.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 检查以往成绩
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
public class BgOfflineSignReviewCheckPreviousResultRsp {

    @Schema(description = "成绩ID")
    private Long resultId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "赛事名称")
    private String raceName;

    @Schema(description = "赛事时间")
    private Long raceTime;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "净成绩，单位秒")
    private Long pureDuration;

}
