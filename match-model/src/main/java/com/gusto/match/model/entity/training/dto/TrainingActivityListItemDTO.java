package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 训练活动
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data

@NoArgsConstructor
public class TrainingActivityListItemDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "训练活动名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "已报名人数")
    private Integer signCount;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "状态 0-默认 1-进行中 2-报名中 3-已经结束")
    private Integer activityState;
}
