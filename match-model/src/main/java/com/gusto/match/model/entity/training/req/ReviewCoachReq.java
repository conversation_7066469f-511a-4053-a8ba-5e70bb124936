package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/1
 */
@Data

public class ReviewCoachReq {
    @Schema(description = "新学员申请记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> recordIds;

    @Schema(description = "审核状态：0-全部 1-待审核 2-通过 3-拒绝", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer state;

    @Schema(description = "拒绝原因")
    private String reason;
}
