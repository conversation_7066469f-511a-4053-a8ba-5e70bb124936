package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取房间列表
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Data
public class BgLocationShareMessageQueryReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long roomId = 0L;

    @Schema(description = "昵称")
    private String nickname = "";

    @Schema(description = "手机")
    private String mobile = "";

    @Schema(description = "用户ID")
    private Long userId = 0L;

    @Schema(description = "内容")
    private String content = "";

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
