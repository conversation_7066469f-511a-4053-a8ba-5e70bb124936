package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 社区话题
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityTopicListElementDTO {

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛")
    private Integer primaryCategory;

    @Schema(description = "二级分类：0-默认 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer secondaryCategory;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "真实点击量")
    private Integer realClickCount;

    @Schema(description = "是否热门")
    private Boolean hot;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布")
    private Integer publishSetting;

    @Schema(description = "发布时间")
    private Long publishTime;

    @Schema(description = "创建时间")
    private Long createTime;

}
