package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * <p>
 * 社区禁言
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityBlockDTO {

    @Schema(description = "禁言ID")
    private Long blockId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论 3-用户")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "时长类型：0-默认 1-1天 2-7天 3-30天 4-永久")
    private Integer durationType;

    @Schema(description = "原因类型：0-默认 1-涉黄信息 2-有害信息 3-人身攻击 4-违法信息 5-不实信息 6-违规营销 7-其他")
    private String reasonType;

    @Schema(description = "原因内容")
    private String reasonContent;

    @Schema(description = "禁言时间")
    private Instant blockTime;

}
