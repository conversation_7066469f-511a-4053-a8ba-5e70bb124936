package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

/**
 * 更新标签排序
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgUpdateCommunityTagSortReq {

    @Schema(description = "标签ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long tagId;

    @Schema(description = "排序，降序", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Integer sort;

}
