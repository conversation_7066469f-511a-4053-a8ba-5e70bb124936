package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 线下赛用户报名退款记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_sign_refund_record")
public class OfflineUserSignRefundRecord extends BaseTenantEntity {

    private static final long serialVersionUID = -2796888636784680982L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "操作人类型：0-默认 1-用户 2-管理员 3-系统")
    private Integer operatorType;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "操作人IP")
    private String operatorIp;

    @Schema(description = "退款记录类型：0-默认 1-退赛 2-更改项目 3-转让名额 4-不中签 5-审核不通过 6-退差价 7-订单退款")
    private Integer recordType;

    @Schema(description = "应退款金额，单位元")
    private BigDecimal refundAmount;

    @Schema(description = "实退款金额，单位元")
    private BigDecimal actualRefundAmount;

    @Schema(description = "平台支付单号")
    private String tradeNo;

    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "商户退款单号")
    private String outRefundId;

    @Schema(description = "平台退款单号")
    private String refundId;

    @Schema(description = "退款状态：0-默认 1-已申请 2-已退款 3-退款中 4-退款失败")
    private Integer refundState;

    @Schema(description = "退款时间")
    private Instant refundTime;

    @Schema(description = "退款类型：0-默认 1-系统 2-手动")
    private Integer refundType;

    @Schema(description = "管理员备注")
    private String adminRemark;

    @Schema(description = "转账退款账户名")
    private String refundAccount;

    @Schema(description = "退款账户名")
    private String refundName;

    @Schema(description = "应退款报名费，单位元，用于计算报名费已退总和")
    private BigDecimal refundSignAmount;

    @Schema(description = "订单ID列表，目前只有后台选择按订单退款时才有值，所选订单全退")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> orderIdList;

    @Schema(description = "报名选号是否发生退款")
    private Boolean refundSignSelectNumber;

    @Schema(description = "金额类型：0-默认 1-人名币 2-美元")
    private Integer amountType;

}
