package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <p>
 * 提交挑战赛报名请求-商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SubmitChallengeUserSignGoodsDetailReq {

    @Schema(description = "商品", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long goodsId;

    @Schema(description = "规格索引", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private String specIndex;

    @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long quantity;

}
