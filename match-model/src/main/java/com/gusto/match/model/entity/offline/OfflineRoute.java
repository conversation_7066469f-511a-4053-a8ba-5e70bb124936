package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛路线
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_route")
public class OfflineRoute extends BaseTenantEntity {

    private static final long serialVersionUID = -5103578454667475861L;

    @Schema(description = "路线ID")
    @TableId(type = IdType.AUTO)
    private Long routeId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "关门用时")
    private String duration;

    @Schema(description = "起点")
    private String startPoint;

    @Schema(description = "终点")
    private String endPoint;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "路线链接")
    private String routeUrl;

    @Schema(description = "是否启用")
    private Boolean enable;

    @Schema(description = "排序，降序")
    private Integer sort;

}
