package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 后台-赛事项目
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
public class BgProjectDTO {

    @Schema(description = "项目ID，更新时必填")
    private Long projectId;

    @Schema(description = "赛事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long matchId;

    @Schema(description = "赛事赛季ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seasonId;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "支付金额：0为免费，不用支付", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal price;

    @Schema(description = "项目状态：0-未上线 1-上线", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer state;

    @Schema(description = "号码数字长度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer bibnoLength;

    @Schema(description = "号码前缀", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bibnoSub;

    @Schema(description = "号码初始值", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long bibnoInitNumber;

    @Schema(description = "号码自增最大值", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer bibnoIncreaseMax;

    @Schema(description = "自增规则：0-按增幅增加 1-随机", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer bibnoIncreaseRule;

    @Schema(description = "号码布模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long bibnoTemplateId;

    @Schema(description = "证书模板ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long certTemplateId;

    @Schema(description = "发起陪跑证书模板ID")
    private Long ownTemplateId;

    @Schema(description = "项目绑定套餐列表")
    private List<BgProjectPackageDTO> projectPackageList;

    @Schema(description = "是否开放陪跑", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean withRunning;

    @Schema(description = "参与陪跑证书模板ID")
    private Long accompanyTemplateId;

    @Schema(description = "项目类型：0-个人赛 1-助力赛 2-个人限时赛", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer projectType;

    @Schema(description = "挑战目标距离，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double targetDistance;

    @Schema(description = "目标类型：0-固定 1-定长减年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer targetType;

    @Schema(description = "（助力）队伍人数限制：0为不限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer memberLimit;

    @Schema(description = "（助力）我的助力人数限制：0为不限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer helpLimit;

    @Schema(description = "（助力）助力时间限制，单位小时", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer hourLimit;

    @Schema(description = "（个人限时）单次最短距离，单位米，0为不限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double minDistance;

    @Schema(description = "（个人限时）限时类型：0-默认 1-每天 2-每周 3-每月", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer timeLimitType;

    @Schema(description = "挑战目标", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetDesc;

    @Schema(description = "证书背景图", requiredMode = Schema.RequiredMode.REQUIRED)
    private String certBackgroundImage;

    @Schema(description = "发起陪跑证书模板背景图", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ownBackgroundImage;

    @Schema(description = "参与陪跑证书模板背景图", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accompanyBackgroundImage;

    @Schema(description = "号码布背景", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bibnoBackgroundImage;

    @Schema(description = "号码前缀颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bibnoSubColor;

    @Schema(description = "号码颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bibnoColor;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "封面图片", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coverImage;

    @Schema(description = "性别：0-默认 1-男 2-女", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sex;

    @Schema(description = "头图-报名成功", requiredMode = Schema.RequiredMode.REQUIRED)
    private String headImageForSignSuccess;

    @Schema(description = "头图-挑战成功", requiredMode = Schema.RequiredMode.REQUIRED)
    private String headImageForSuccess;

    @Schema(description = "头图-挑战失败", requiredMode = Schema.RequiredMode.REQUIRED)
    private String headImageForFailure;

    @Schema(description = "结束订单时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long endOrderTime;

}
