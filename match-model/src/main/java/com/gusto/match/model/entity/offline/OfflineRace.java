package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 线下赛
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_race", autoResultMap = true)
public class OfflineRace extends BaseTenantEntity {

    private static final long serialVersionUID = -1886408645072239717L;

    @Schema(description = "赛事ID")
    @TableId(type = IdType.AUTO)
    private Long raceId;

    @Schema(description = "赛事类型：0-默认 1-马拉松 2-越野赛 3-铁人三项 4-徒步 5-欢乐跑 6-骑行")
    private Integer raceType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "比赛城市简称")
    private String shortAddress;

    @Schema(description = "报名开始时间")
    private Instant signStartTime;

    @Schema(description = "报名截止时间")
    private Instant signEndTime;

    @Schema(description = "赛事开始时间")
    private Instant startTime;

    @Schema(description = "赛事截止时间")
    private Instant endTime;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "分享图")
    private String shareImage;

    @Schema(description = "报名设置：0-默认 1-仅限个人 2-个人和团报均可 3-仅限团报")
    private Integer signSetting;

    @Schema(description = "问卷：0-默认 1-不设置 2-设置")
    private Integer quizSetting;

    @Schema(description = "问卷ID")
    private Long quizId;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布 3-内部测试 4-赛事已延期 5-赛事已取消")
    private Integer publishSetting;

    @Schema(description = "公告")
    private String notice;

    @Schema(description = "内部邀请码")
    private String magicCode;

    @Schema(description = "加购商品设置：0-默认 1-不设置 2-设置")
    private Integer shopSetting;

    @Schema(description = "加购商品标题")
    private String shopTitle;

    @Schema(description = "加购商品子标题")
    private String shopSubTitle;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "关联文本")
    private String relationContent;

    @Schema(description = "是否额满即止")
    private Boolean signFullStop;

    @Schema(description = "抽签公示开始时间")
    private Instant ballotStartTime;

    @Deprecated(since = "兼容")
    @Schema(description = "抽签公示截止时间")
    private Instant ballotEndTime;

    @Schema(description = "是否对外可见")
    private Boolean foreignVisible;

    @Deprecated(since = "兼容")
    @Schema(description = "修改截止时间")
    private Instant modifyEndTime;

    @Schema(description = "展示报名开始时间")
    private Instant showSignStartTime;

    @Schema(description = "展示报名截止时间")
    private Instant showSignEndTime;

    @Schema(description = "APP发布状态：0-默认 1-申请中 2-已通过 3-不通过 4-未申请")
    private Integer appPublishState;

    @Schema(description = "退赛设置：0-默认 1-不设置 2-设置")
    private Integer quitSetting;

    @Schema(description = "退赛开始时间")
    private Instant quitStartTime;

    @Schema(description = "退赛截止时间")
    private Instant quitEndTime;

    @Deprecated(since = "兼容")
    @Schema(description = "允许退赛类型：0-默认 1-所有 2-规定时间内报名")
    private Integer allowQuitSetting;

    @Deprecated(since = "兼容")
    @Schema(description = "允许退赛报名开始时间")
    private Instant allowStartTime;

    @Deprecated(since = "兼容")
    @Schema(description = "允许退赛报名截止时间")
    private Instant allowEndTime;

    @Schema(description = "退款账号填写设置：0-默认 1-必填 2-微信报名不用填 3-仅第三方报名的人必填")
    private Integer refundInputSetting;

    @Schema(description = "冷静期，单位天，冷静期从完成支付的时间开始算，允许申请全额退款，受到退赛截止时间限制")
    private Integer calmDayCount;

    @Schema(description = "自由退赛设置：0-默认 1-不设置 2-设置")
    private Integer freeQuitSetting;

    @Schema(description = "自由退赛手续费类型：0-默认 1-金额 2-折扣")
    private Integer freeQuitServiceAmountType;

    @Schema(description = "自由退赛手续费")
    private BigDecimal freeQuitServiceAmount;

    @Schema(description = "退赛说明")
    private String quitContent;

    @Schema(description = "更改项目设置：0-默认 1-不设置 2-设置")
    private Integer changeProjectSetting;

    @Schema(description = "更改项目类型：0-默认 1-仅当前通道 2-可跨通道")
    private Integer changeProjectType;

    @Schema(description = "更改项目截止时间")
    private Instant changeProjectEndTime;

    @Schema(description = "更改项目手续费")
    private BigDecimal changeProjectServiceAmount;

    @Schema(description = "更改项目说明")
    private String changeProjectContent;

    @Schema(description = "转让名额设置：0-默认 1-不设置 2-设置")
    private Integer transferSetting;

    @Schema(description = "转让名额截止时间")
    private Instant transferEndTime;

    @Schema(description = "转让名额手续费")
    private BigDecimal transferServiceAmount;

    @Schema(description = "转让名额说明")
    private String transferContent;

    @Schema(description = "问卷设置：0-默认 1-不设置 2-设置")
    private Integer surveySetting;

    @Schema(description = "问卷收集开始时间")
    private Instant surveyStartTime;

    @Schema(description = "问卷收集截止时间")
    private Instant surveyEndTime;

    @Schema(description = "问卷ID")
    private Long surveyId;

    @Schema(description = "标签ID-已支付")
    private Long tagIdForPay;

    @Schema(description = "标签ID-审核通过")
    private Long tagIdForPass;

    @Schema(description = "标签ID-审核不通过")
    private Long tagIdForNotPass;

    @Schema(description = "标签ID-报名成功")
    private Long tagIdForSignSuccess;

    @Schema(description = "标签ID-已中签")
    private Long tagIdForBallot;

    @Schema(description = "标签ID-未中签")
    private Long tagIdForNotBallot;

    @Schema(description = "标签ID-完赛")
    private Long tagIdForFinish;

    @Schema(description = "标签ID-退款成功")
    private Long tagIdForRefundSuccess;

    @Schema(description = "选号设置：0-默认 1-不设置 2-设置")
    private Integer selectNumberSetting;

    @Schema(description = "选号开始时间")
    private Instant selectNumberStartTime;

    @Schema(description = "选号截止时间")
    private Instant selectNumberEndTime;

    @Schema(description = "选号单价")
    private BigDecimal selectNumberAmount;

    @Schema(description = "星级加价")
    private BigDecimal selectNumberStarAmount;

    @Schema(description = "选号单价原价")
    private BigDecimal selectNumberOriginAmount;

    @Schema(description = "星级加价原价")
    private BigDecimal selectNumberStarOriginAmount;

    @Schema(description = "关注类型：0-不设置 1-第一赛道公众号 2-私域企微好友")
    private Integer followType;

    @Schema(description = "关注跳转链接")
    private String followUrl;

    @Schema(description = "小程序关注跳转链接")
    private String miniProgramFollowUrl;

    @Schema(description = "结算状态：0-默认 1-未结算 2-已结算")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer settleState = 1;

    @Schema(description = "短信-审核不通过设置：0-默认 1-不设置 2-设置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer smsReviewDeniedSetting = 2;

    @Schema(description = "短信-审核不通过模板")
    private String smsReviewDeniedTemplate;

    @Schema(description = "短信-待审核设置：0-默认 1-不设置 2-设置")
    private Integer smsToReviewSetting;

    @Schema(description = "短信-待审核模板")
    private String smsToReviewTemplate;

    @Schema(description = "短信-报名成功设置：0-默认 1-不设置 2-设置")
    private Integer smsSignSuccessSetting;

    @Schema(description = "短信-报名成功模板")
    private String smsSignSuccessTemplate;

    @Schema(description = "短信-待抽签设置：0-默认 1-不设置 2-设置")
    private Integer smsWaitBallotSetting;

    @Schema(description = "短信-待抽签模板")
    private String smsWaitBallotTemplate;

    @Schema(description = "是否推荐资格赛")
    private Boolean recommendQualification;

    @Schema(description = "比赛项目列表")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> projectList;

    @Schema(description = "赛事规模")
    private String eventSize;

    @Schema(description = "分享图-待抽签")
    private String shareImageForWaitBallot;

    @Schema(description = "分享图-已中签")
    private String shareImageForBallot;

    @Schema(description = "分享图-报名成功")
    private String shareImageForSignSuccess;

    @Schema(description = "标签ID-项目1")
    private Long tagIdForProject1;

    @Schema(description = "标签ID-项目2")
    private Long tagIdForProject2;

    @Schema(description = "标签ID-项目3")
    private Long tagIdForProject3;

    @Schema(description = "标签ID-项目4")
    private Long tagIdForProject4;

    @Schema(description = "赛前问卷设置：0-默认 1-不设置 2-设置")
    private Integer preSurveySetting = 1;

    @Schema(description = "赛前问卷收集开始时间")
    private Instant preSurveyStartTime = null;

    @Schema(description = "赛前问卷收集截止时间")
    private Instant preSurveyEndTime = null;

    @Schema(description = "赛前问卷ID")
    private Long preSurveyId = 0L;

    @Schema(description = "比赛城市简称（英文）")
    private String shortAddressEn;

    @Schema(description = "名称（英文）")
    private String nameEn;

    @Schema(description = "报名选号设置：0-默认 1-不设置 2-设置")
    private Integer signSelectNumberSetting;

    @Schema(description = "报名选号开始时间")
    private Instant signSelectNumberStartTime;

    @Schema(description = "报名选号截止时间")
    private Instant signSelectNumberEndTime;

    @Schema(description = "报名选号单价")
    private BigDecimal signSelectNumberAmount;

    // TODO race add sql

    @Schema(description = "是否允许跳过问卷")
    private Boolean allowSkipQuiz;

    /**
     * 获取是否展示在app上
     */
    public Boolean showInApp() {
        return appPublishState == 2 && foreignVisible;
    }

    /**
     * 获取赛事状态
     */
    public Integer getRaceState() {
        var now = Instant.now();
        var raceState = 0;
        var currentSignStartTime = showSignStartTime;
        if (currentSignStartTime == null) {
            currentSignStartTime = signStartTime;
        }
        var currentSignEndTime = showSignEndTime;
        if (currentSignEndTime == null) {
            currentSignEndTime = signEndTime;
        }
        if (now.isAfter(currentSignStartTime) && now.isBefore(currentSignEndTime)) {
            // 报名进行中
            raceState = 1;
        } else if (now.isAfter(currentSignEndTime) && now.isBefore(startTime)) {
            // 报名已结束
            raceState = 2;
        } else if (now.isAfter(startTime) && now.isBefore(endTime)) {
            // 赛事进行中
            raceState = 3;
        } else if (now.isAfter(endTime)) {
            // 赛事已结束
            raceState = 4;
        } else {
            // 报名未开始
            raceState = 7;
        }
        if (publishSetting == 4) {
            // 赛事已延期
            raceState = 5;
        } else if (publishSetting == 5) {
            // 赛事已取消
            raceState = 6;
        }
        return raceState;
    }

    /**
     * 是否可以选号
     */
    public Boolean canSelectNumber() {
        if (selectNumberStartTime == null || selectNumberEndTime == null) {
            return false;
        }
        var now = Instant.now();
        return selectNumberSetting == 2 && now.isAfter(selectNumberStartTime) && now.isBefore(selectNumberEndTime);
    }

    /**
     * 是否可以选号
     */
    public Boolean canSignSelectNumber() {
        if (signSelectNumberStartTime == null || signSelectNumberEndTime == null) {
            return false;
        }
        var now = Instant.now();
        return signSelectNumberSetting == 2 && now.isAfter(signSelectNumberStartTime) && now.isBefore(signSelectNumberEndTime);
    }

    /**
     * 根据距离和赛事类型获取项目标签ID
     */
    public Long getProjectTagIdFromDistanceOrNull(Double distance) {
        // 换算为整公里
        var distanceKm = Math.floor(distance / 1000);
        switch (raceType) {
            case 1:
                if (distanceKm < 10) {
                    return tagIdForProject1;
                } else if (distanceKm >= 10 && distanceKm <= 20) {
                    return tagIdForProject2;
                } else if (distanceKm == 21) {
                    return tagIdForProject3;
                } else if (distanceKm == 42) {
                    return tagIdForProject4;
                } else {
                    return null;
                }
            case 2:
                if (distanceKm < 20) {
                    return tagIdForProject1;
                } else if (distanceKm >= 20 && distanceKm <= 45)
                    return tagIdForProject2;
                else if (distanceKm >= 46 && distanceKm <= 80) {
                    return tagIdForProject3;
                } else if (distanceKm > 80) {
                    return tagIdForProject4;
                } else {
                    return null;
                }
            case 3:
                if (distanceKm == 51) {
                    return tagIdForProject1;
                } else if (distanceKm == 113) {
                    return tagIdForProject2;
                } else if (distanceKm == 226) {
                    return tagIdForProject3;
                } else {
                    return 0L;
                }
            default:
                return null;
        }
    }

}
