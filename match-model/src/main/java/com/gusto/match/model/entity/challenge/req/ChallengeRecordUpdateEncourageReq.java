package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 更新鼓励
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeRecordUpdateEncourageReq {

    @Schema(description = "陪跑ID")
    @Positive
    private Long recordId;

    @Schema(description = "挑战失败的鼓励")
    private String encourage;

}
