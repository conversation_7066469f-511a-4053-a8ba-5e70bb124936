package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/9
 */
@Data

public class CampSearchElementDTO {
    @Schema(description = "训练营ID")
    private Long campId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "训练营类型：0-默认 1-公开 2-仅学员")
    private Integer campType;

    @Schema(description = "已加入人数")
    private Integer peopleCount;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "是否官方")
    private Boolean official;

    @Schema(description = "活动标签")
    private String type;
}
