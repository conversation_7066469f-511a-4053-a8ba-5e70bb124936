package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 教练申请记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coach_application")
public class CoachApplication extends BaseEntity {
    private static final long serialVersionUID = 7614642699313184858L;

    @TableId(value = "record_id", type = IdType.AUTO)
    @Schema(description = "教练申请记录ID")
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "开始跑步时间")
    private Instant runStartTime;

    @Schema(description = "教练编号(自动获取)")
    private String coachNumber;

    @Schema(description = "参与的万名教练计划")
    private Long planId;

    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Schema(description = "审核状态：0-默认 1-待审核 2-通过 3-拒绝")
    private Integer state;

    @Schema(description = "判断提示信息是否已经展示")
    private Boolean isShow;

    @Schema(description = "是否新教练")
    private Boolean newCoach;

    @Schema(description = "自我介绍")
    private String introduce;
}
