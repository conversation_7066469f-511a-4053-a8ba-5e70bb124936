package com.gusto.match.model.entity.sunshine.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * 报名陪跑请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineJoinUserAccompanyRunReq {

    @Schema(description = "陪跑记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long recordId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String username;

    @Schema(description = "陪跑祝福")
    @Size(max = 50)
    private String blessing;

    @Schema(description = "陪跑方式：0-默认 1-到场陪跑 2-异地陪跑", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer accompanyMode;

    @Schema(description = "陪跑距离，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double accompanyDistance;
}
