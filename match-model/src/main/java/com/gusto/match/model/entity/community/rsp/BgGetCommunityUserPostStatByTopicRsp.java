package com.gusto.match.model.entity.community.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 根据话题获取官方动态统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class BgGetCommunityUserPostStatByTopicRsp {

    @Schema(description = "动态总数")
    private Long postCount;

    @Schema(description = "评论总数")
    private Long commentCount;

    @Schema(description = "分享总数")
    private Long shareCount;

    @Schema(description = "点赞总数")
    private Long likeCount;

    @Schema(description = "上周动态总数")
    private Long lastWeekPostCount;

    @Schema(description = "上周评论总数")
    private Long lastWeekCommentCount;

    @Schema(description = "上周分享总数")
    private Long lastWeekShareCount;

    @Schema(description = "上周点赞总数")
    private Long lastWeekLikeCount;

}
