package com.gusto.match.model.entity.location.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMemberItemDTO {

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "是否房主")
    private Boolean owner;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "最近更新位置时间")
    private Long lastUpdateLocationTime;

    @Schema(description = "运动时间，单位秒")
    private Long duration;

    @Schema(description = "运动距离，单位米")
    private Double distance;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "实时配速，单位秒")
    private Double currentPace;

    @Schema(description = "平均步频，单位步/分钟")
    private Integer averageStepRate;

    @Schema(description = "平均步幅，单位厘米")
    private Double averageStride;

    @Schema(description = "累计上升，单位米")
    private Double totalAscent;

    @Schema(description = "方向")
    private Double direction;

    @Schema(description = "是否运动中")
    private Boolean moving;

    @Schema(description = "是否展示方向")
    private Boolean showDirection;

}
