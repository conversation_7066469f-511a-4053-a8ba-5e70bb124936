package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/8
 */
@Data

public class BgFullCoachDTO {
    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "教练等级")
    private Integer level;

    @Schema(description = "参与的万名教练计划")
    private String joinTenThousandCoaches;

    @Schema(description = "学员数量")
    private Integer studentNumber;

    @Schema(description = "训练活动数量")
    private Integer activityNumber;

    @Schema(description = "是否锁定")
    private Boolean locked;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "是否以推荐")
    private Boolean recommend;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "昵称")
    private String nickname;

    // v1.3
    @Schema(description = "用户ID")
    private Long userId;

    // v1.3
    @Schema(description = "拼音名字")
    private String pinyinName;
}
