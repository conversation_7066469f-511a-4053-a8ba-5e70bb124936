package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛实时排名明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_real_time_ranking_detail")
public class OfflineRealTimeRankingDetail extends BaseEntity {

    private static final long serialVersionUID = -8510018667662830965L;

    @Schema(description = "唯一ID")
    @TableId(type = IdType.INPUT)
    private String uniqueId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "排名")
    private Integer rank;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "距离，单位米")
    private Double distance;

    @Schema(description = "用时，单位秒")
    private Integer duration;

    @Schema(description = "用时字符串")
    private String durationString;

    @Schema(description = "用时毫秒数")
    private Long durationMillisecond;

    @Schema(description = "未减时用时，单位秒")
    private Integer rawDuration;

    @Schema(description = "未减时用时字符串")
    private String rawDurationString;

    @Schema(description = "赛虎运动员ID")
    private Long tigerAthleteId;

}
