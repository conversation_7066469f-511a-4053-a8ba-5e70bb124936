package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <p>
 * 线下赛用户报名选号记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_select_number_record")
public class OfflineSelectNumberRecord extends BaseTenantEntity {

    private static final long serialVersionUID = 9102265792275383811L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "号码ID")
    private Long bibnoId;

    @Schema(description = "长度")
    private Integer length;

    @Schema(description = "号码")
    private Integer number;

    @Schema(description = "去除前缀的完整号码")
    private String pureNumber;

    @Schema(description = "星级")
    private Integer star;

    @Schema(description = "选号单价")
    private BigDecimal numberAmount;

    @Schema(description = "星级加价")
    private BigDecimal starAmount;

    @Schema(description = "总金额，等于选号单价+星级*星级加价")
    private BigDecimal totalAmount;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号（我们生成）")
    private String outTradeNo;

    @Schema(description = "交易单号（支付平台返回）")
    private String tradeNo;

    @Schema(description = "支付状态：0-默认 1-未支付 2-已支付 3-已退款 4-已部分退款")
    private Integer payState;

    @Schema(description = "支付金额，单位元")
    private BigDecimal payAmount;

    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "支付时间，单位毫秒")
    private Instant payTime;

    @Schema(description = "订单状态：0-默认 1-未支付 2-已支付 3-已发货 4-确认收货 5-已退款 6-已取消 7-已部分退款 8-交易结束")
    private Integer orderState;

    @Schema(description = "记录类型：0-默认 1-选号 2-报名选号")
    private Integer recordType;

}
