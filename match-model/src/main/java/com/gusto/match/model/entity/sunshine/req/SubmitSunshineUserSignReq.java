package com.gusto.match.model.entity.sunshine.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.util.List;

/**
 * <p>
 * 提交挑战赛报名请求
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SubmitSunshineUserSignReq {

    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long projectId;

    @Schema(description = "报名卡ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long userCardId;

    @Schema(description = "收货人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shipUsername;

    @Schema(description = "收货人手机", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shipMobile;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String address;

    @Schema(description = "商品明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SubmitSunshineUserSignGoodsDetailReq> goodsDetailList;

    @Schema(description = "所属跑团")
    private String clubName;

    @Schema(description = "套餐下标，从0开始", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Integer signPackageIndex;

    @Schema(description = "挑战地点")
    private String challengeLocation;

    @Schema(description = "监护人报名卡ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long guardiansUserCardId;
}
