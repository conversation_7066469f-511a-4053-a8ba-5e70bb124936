package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 后台-创建相册请求
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgCreatePhotoAlbumReq {

    @Schema(description = "关联的线下赛赛事ID", required = true)
    @NotNull(message = "线下赛ID不能为空")
    private Long offlineRaceId;

    @Schema(description = "相册名称", required = true)
    @NotBlank(message = "相册名称不能为空")
    private String albumName;

    @Schema(description = "相册描述")
    private String albumDescription;

    @Schema(description = "谱时相册ID列表", required = true)
    @NotEmpty(message = "谱时相册ID列表不能为空")
    private List<String> photoPlusAlbumIds;

    @Schema(description = "摄影师分成比例（百分比，如30表示30%）", required = true)
    @NotNull(message = "摄影师分成比例不能为空")
    @DecimalMin(value = "0", message = "摄影师分成比例不能小于0")
    @DecimalMax(value = "100", message = "摄影师分成比例不能大于100")
    private BigDecimal photographerShareRatio;

    @Schema(description = "公司分成比例（百分比，如70表示70%）", required = true)
    @NotNull(message = "公司分成比例不能为空")
    @DecimalMin(value = "0", message = "公司分成比例不能小于0")
    @DecimalMax(value = "100", message = "公司分成比例不能大于100")
    private BigDecimal companyShareRatio;

    @Schema(description = "单张照片价格（元）", required = true)
    @NotNull(message = "单张照片价格不能为空")
    @Positive(message = "单张照片价格必须大于0")
    private BigDecimal singlePhotoPrice;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status = 1;

    @Schema(description = "相册封面图片URL")
    private String coverImageUrl;

    @Schema(description = "排序权重，数值越大排序越靠前")
    private Integer sortWeight = 0;

    @Schema(description = "备注信息")
    private String remark;
}
