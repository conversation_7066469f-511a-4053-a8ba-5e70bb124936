package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区点赞
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_like")
public class CommunityLike extends BaseEntity {

    private static final long serialVersionUID = 7357145528183956620L;

    @Schema(description = "点赞ID")
    @TableId(type = IdType.AUTO)
    private Long likeId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "对象作者类型：0-默认 1-用户 2-官方")
    private Integer objectAuthorType;

    @Schema(description = "对象作者ID")
    private Long objectAuthorId;

    @Schema(description = "是否已读")
    @TableField("`read`")
    private Boolean read;

    @Schema(description = "摘要")
    private String summary;

}
