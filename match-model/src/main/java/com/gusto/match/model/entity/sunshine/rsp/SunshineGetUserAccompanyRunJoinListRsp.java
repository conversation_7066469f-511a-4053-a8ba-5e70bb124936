package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取用户发起的陪跑列表响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetUserAccompanyRunJoinListRsp {

    @Schema(description = "陪跑列表")
    private List<SunshineGetUserJoinAccompanyRunRsp> accompanyList;

    @Schema(description = "总陪跑人数")
    private Integer totalAccompanyCount;

    @Schema(description = "到场人数")
    private Integer localCount;

    @Schema(description = "异地人数")
    private Integer remoteCount;

    @Schema(description = "是否报名")
    private Boolean sign;
}
