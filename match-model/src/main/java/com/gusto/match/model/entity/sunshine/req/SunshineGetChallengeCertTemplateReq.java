package com.gusto.match.model.entity.sunshine.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 挑战赛获取完赛证书请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeCertTemplateReq {

    @Schema(description = "赛季ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long seasonId;

    @Schema(description = "成绩ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long resultId;

    @Schema(description = "证书类型：0-默认 1-完赛证书 2-发起者完赛陪跑证书 3-陪跑者陪跑证书", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer certType;

    @Schema(description = "陪跑记录ID (陪跑者陪跑证书必传)")
    private Long recordId;
}
