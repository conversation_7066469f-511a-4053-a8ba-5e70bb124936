package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取挑战赛同城挑战者响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeLocalRsp {

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "完赛状态：0-默认 1-挑战中 2-挑战成功 3-挑战失败 4-挑战未开始")
    private Integer finishedState;

    @Schema(description = "总陪跑人数")
    private Integer totalAccompanyMemberCount;

    @Schema(description = "城市")
    private String city;
}
