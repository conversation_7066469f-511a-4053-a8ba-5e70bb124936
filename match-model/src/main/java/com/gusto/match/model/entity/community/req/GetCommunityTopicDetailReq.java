package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 获取话题详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommunityTopicDetailReq {

    @Parameter(description = "话题ID", required = true)
    @Positive
    private Long topicId;

    @Parameter(description = "排序类型：0-默认 1-最热 2-最新")
    private Integer sortType = 1;


}
