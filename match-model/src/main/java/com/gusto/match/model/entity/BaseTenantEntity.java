package com.gusto.match.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 租户基类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseTenantEntity extends BaseEntity {

    private static final long serialVersionUID = 8772099465583202047L;

    @Schema(description = "多租户ID")
    private Long tenantId;

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

}
