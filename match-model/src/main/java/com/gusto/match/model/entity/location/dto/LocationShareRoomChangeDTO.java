package com.gusto.match.model.entity.location.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareRoomChangeDTO {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "成员人数限制，为0表示无限制")
    private Integer memberLimit;

    @Schema(description = "共享人数限制，为0表示无限制")
    private Integer shareLimit;

    @Schema(description = "轨迹链接")
    private String trackUrl;

    @Schema(description = "最近更新时间")
    private Long lastUpdateTime;

    @Schema(description = "状态：0-默认 1-未开始 2-进行中 3-已结束")
    private Integer state;

}
