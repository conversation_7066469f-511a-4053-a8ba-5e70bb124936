package com.gusto.match.model.entity.photo.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * 相册列表响应
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhotoAlbumListRsp {

    @Schema(description = "相册ID")
    private Long albumId;

    @Schema(description = "关联的线下赛赛事ID")
    private Long offlineRaceId;

    @Schema(description = "相册名称")
    private String albumName;

    @Schema(description = "相册描述")
    private String albumDescription;

    @Schema(description = "谱时相册ID列表")
    private List<String> photoPlusAlbumIds;

    @Schema(description = "单张照片价格（元）")
    private BigDecimal singlePhotoPrice;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "相册封面图片URL")
    private String coverImageUrl;

    @Schema(description = "排序权重，数值越大排序越靠前")
    private Integer sortWeight;

    @Schema(description = "创建时间")
    private Instant createTime;

    @Schema(description = "更新时间")
    private Instant updateTime;
}
