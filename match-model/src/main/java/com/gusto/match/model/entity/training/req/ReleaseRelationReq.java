package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/27
 */
@Data

public class ReleaseRelationReq {
    @Schema(description = "目标用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "类型：0-默认 1-教练解除学员关系 2-学员解除教练关系", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;
}
