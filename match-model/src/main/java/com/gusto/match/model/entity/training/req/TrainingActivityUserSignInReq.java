package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练活动用户签到请求
 *
 * <AUTHOR>
 * @since 2021/8/25
 */
@Data

public class TrainingActivityUserSignInReq {
    @Schema(description = "训练活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long activityId;

    @Schema(description = "是否签到成功", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean state;
}
