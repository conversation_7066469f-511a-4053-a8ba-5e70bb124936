package com.gusto.match.model.entity.sunshine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 挑战赛用户成绩
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineUserResultDTO {
    @Schema(description = "成绩ID")
    private Long resultId;

    @Schema(description = "实际挑战距离，单位米")
    private Double actualDistance;

    @Schema(description = "时长，单位秒")
    private Long duration;

    @Schema(description = "平均配速")
    private Double averagePace;

    @Schema(description = "完赛时间")
    private Long finishedTime;

    @Schema(description = "陪跑ID")
    private Long recordId;

}
