package com.gusto.match.model.entity.community.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 社区用户动态
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityUserPostListElementDTO {

    @Schema(description = "动态ID")
    @TableId(type = IdType.AUTO)
    private Long postId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "作者类型：0-默认 1-用户 2-官方")
    private Integer authorType;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "标题，限制20字")
    private String title;

    @Schema(description = "内容，限制1000字")
    private String content;

    @Schema(description = "图片列表，限制9张")
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "分享量")
    private Integer shareCount;

    @Schema(description = "关联的跑步记录ID")
    private Long runRecordId;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer activityType;

    @Schema(description = "总距离，单位米")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    private Integer totalDuration;

    @Schema(description = "设备")
    private String deviceModel;

    @Schema(description = "运动天数")
    private Integer runDayCount;

    @Schema(description = "IP")
    private String ip;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "IP市")
    private String ipCity;

    @Schema(description = "发布时间")
    private Long postTime;

    @Schema(description = "最后修改时间")
    private Long lastUpdateTime;

    @Schema(description = "最后评论时间")
    private Long lastReplyTime;

    @Schema(description = "状态：0-默认 1-展示 2-用户删除 3-管理员删除 4-限流 5-审核不通过")
    private Integer state;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

}
