package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 获取新消息列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMessageGetNewListReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long roomId;

    @Schema(description = "是否第一次进入房间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean firstEnterRoom;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
