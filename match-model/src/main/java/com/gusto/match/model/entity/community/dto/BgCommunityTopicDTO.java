package com.gusto.match.model.entity.community.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;

/**
 * 社区话题
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgCommunityTopicDTO {

    @Schema(description = "话题ID，更新时必填")
    private Long topicId;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String name;

    @Schema(description = "封面图", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String coverImage;

    @Schema(description = "背景色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String backgroundColor;

    @Schema(description = "赛事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long raceId;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer primaryCategory;

    @Schema(description = "二级分类：0-默认 201-马拉松 202-越野 203-铁人三项 204-徒步", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer secondaryCategory;

    @Schema(description = "点击量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer clickCount;

    @Schema(description = "随机增加起始值，为0表示不随机", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer randomStart;

    @Schema(description = "随机增加结束值，设置时需大于randomStart", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer randomEnd;

    @Schema(description = "是否热门")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hot = false;

    @Schema(description = "排序，降序")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sort = 0;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer publishSetting;

    @Schema(description = "赛事名称")
    private String raceName = "";

}
