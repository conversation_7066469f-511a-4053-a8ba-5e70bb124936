package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 旧线下赛域名
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Data
@TableName("t_event_domain")
public class OldOfflineDomain {

    @Schema(description = "域名ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long domainId;

    @TableField("showname")
    @Schema(description = "名称")
    private String name;

    @Schema(description = "关联文本")
    @TableField("subhost")
    private String relationContent;

}
