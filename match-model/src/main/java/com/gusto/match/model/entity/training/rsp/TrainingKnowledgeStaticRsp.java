package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-训练知识统计
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data

public class TrainingKnowledgeStaticRsp {

    @Schema(description = "视频播放次数")
    private Long videoView;

    @Schema(description = "文章阅读次数")
    private Long articleView;

    @Schema(description = "装备评测阅读次数")
    private Long reviewView;
}
