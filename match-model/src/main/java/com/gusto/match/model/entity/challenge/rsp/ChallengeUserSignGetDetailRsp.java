package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.challenge.dto.ChallengeRecordBlessingDTO;
import com.gusto.match.model.entity.challenge.dto.ChallengeRecordMemberDTO;
import com.gusto.match.model.entity.challenge.dto.ChallengeUserSignDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛用户报名详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class ChallengeUserSignGetDetailRsp {

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    private Long endTime;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

    @Schema(description = "完赛时间，单位毫秒")
    private Long finishedTime;

    @Schema(description = "完赛用时，单位秒")
    private Long finishedDuration;

    @Schema(description = "挑战时间")
    private Long challengeTime;

    @Schema(description = "挑战地点")
    private String challengeLocation;

    @Schema(description = "挑战地点坐标")
    private String challengeCoordinate;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "报名套餐名称")
    private String signPackageName;

    @Schema(description = "最新陪跑记录ID")
    private Long latestRecordId;

    @Schema(description = "最新陪跑记录开始时间")
    private Long latestRecordStartTime;

    @Schema(description = "祝福列表")
    private List<ChallengeRecordBlessingDTO> blessingList;

    @Schema(description = "陪跑成员列表")
    private List<ChallengeRecordMemberDTO> memberList;

    @Schema(description = "陪跑人数")
    private Integer memberCount;

    @Schema(description = "感谢信")
    private String thankLetter;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "是否启用感谢信，兼容旧陪跑，旧陪跑不参与感谢信逻辑")
    private Boolean enableThankLetter;

    @Schema(description = "我的挑战记录列表")
    private List<ChallengeUserSignDTO> signList;

    @Schema(description = "最近参与的陪跑昵称")
    private String accompanyNickname;

    @Schema(description = "最近参与的陪跑里程")
    private Double accompanyDistance;

    @Schema(description = "最近参与的陪跑结束时间")
    private Long accompanyEndTime;

    @Schema(description = "赛事介绍")
    private String detailImage;

    @Schema(description = "规则说明")
    private String rule;

    @Schema(description = "常见问题")
    private String question;

    @Schema(description = "用户是否支付")
    private Boolean pay;

    @Schema(description = "用户是否完赛")
    private Boolean finished;

    @Schema(description = "头图-报名成功")
    private String headImageForSignSuccess;

    @Schema(description = "头图-挑战成功")
    private String headImageForSuccess;

    @Schema(description = "头图-挑战失败")
    private String headImageForFailure;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "挑战距离")
    private Double challengeDistance;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "项目目标距离")
    private Double projectTargetDistance;

}
