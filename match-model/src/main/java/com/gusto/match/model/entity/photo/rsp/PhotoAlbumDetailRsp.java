package com.gusto.match.model.entity.photo.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * 相册详情响应
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhotoAlbumDetailRsp {

    @Schema(description = "相册ID")
    private Long albumId;

    @Schema(description = "关联的线下赛赛事ID")
    private Long offlineRaceId;

    @Schema(description = "相册名称")
    private String albumName;

    @Schema(description = "相册描述")
    private String albumDescription;

    @Schema(description = "谱时相册ID列表")
    private List<String> photoPlusAlbumIds;

    @Schema(description = "摄影师分成比例（百分比，如30表示30%）")
    private BigDecimal photographerShareRatio;

    @Schema(description = "公司分成比例（百分比，如70表示70%）")
    private BigDecimal companyShareRatio;

    @Schema(description = "单张照片价格（元）")
    private BigDecimal singlePhotoPrice;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "相册封面图片URL")
    private String coverImageUrl;

    @Schema(description = "排序权重，数值越大排序越靠前")
    private Integer sortWeight;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "创建时间")
    private Instant createTime;

    @Schema(description = "更新时间")
    private Instant updateTime;

    @Schema(description = "折扣配置列表")
    private List<PhotoAlbumDiscountRsp> discounts;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PhotoAlbumDiscountRsp {

        @Schema(description = "折扣配置ID")
        private Long discountId;

        @Schema(description = "购买数量")
        private Integer quantity;

        @Schema(description = "折扣比例（百分比，如90表示9折）")
        private BigDecimal discountRatio;

        @Schema(description = "折扣后单价（元）")
        private BigDecimal discountPrice;

        @Schema(description = "折扣类型：1-按比例折扣 2-固定价格")
        private Integer discountType;

        @Schema(description = "折扣名称（如：3张9折、5张8折）")
        private String discountName;

        @Schema(description = "是否启用：0-禁用 1-启用")
        private Integer status;

        @Schema(description = "排序权重，数值越小排序越靠前")
        private Integer sortOrder;

        @Schema(description = "备注信息")
        private String remark;
    }
}
