package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/12
 */
@Data

public class BgUpdateCoachOfficialPlanReq {
    @Schema(description = "计划ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long planId;

    @Schema(description = "计划名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "课程开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long classStartTime;

    @Schema(description = "课程结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long classEndTime;
}
