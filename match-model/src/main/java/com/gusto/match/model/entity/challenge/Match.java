package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 挑战赛
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("match_info")
public class Match extends BaseEntity {

    private static final long serialVersionUID = -497500870907942260L;

    @Schema(description = "赛事ID")
    @TableId(value = "match_id", type = IdType.AUTO)
    private Long matchId;

    @Schema(description = "赛事名称")
    private String title;

    @Schema(description = "状态：0-未上线 1-上线")
    private Integer state; // MatchState

    @Schema(description = "赛事类型：0-普通 1-系列赛")
    private Integer matchType; // MatchType

    @Schema(description = "当前赛季ID")
    private Long currentSeasonId;

    @Schema(description = "排序序号，默认为0，越大越靠前")
    private Integer sort;
}
