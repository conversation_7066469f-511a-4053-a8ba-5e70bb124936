package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 加入房间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMemberJoinReq {

    @Schema(description = "房间ID，和房间号二选一")
    private Long roomId = 0L;

    @Schema(description = "房间号，和房间ID二选一")
    private String number = "";

    @Schema(description = "验证码")
    private String verifyCode = "";

}
