package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 旧线下赛问题
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Data
@TableName("gusto_question_answer")
public class OfflineQuestionAnswer implements Serializable {

    private static final long serialVersionUID = -1969730874609494792L;

    @Schema(description = "问题ID")
    @TableId(type = IdType.AUTO)
    private Long questionId;

    @Schema(description = "主题ID")
    private Long titleId;

    @Schema(description = "节点类型 0默认 1报名 2比赛 3赛后")
    private Integer nodeType;

    @Schema(description = "状态 0删除 1正常 2未启用 3用户提问 4已回复但不公示")
    private Integer state;

    @Schema(description = "问题")
    private String question;

    @Schema(description = "答案")
    private String answer;

    @Schema(description = "浏览量")
    private Integer views;

    @Schema(description = "置顶")
    private Integer isTop;

}
