package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 社区禁言
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_block")
public class CommunityBlock extends BaseEntity {

    private static final long serialVersionUID = 4949521589251104869L;

    @Schema(description = "禁言ID")
    @TableId(type = IdType.AUTO)
    private Long blockId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论 3-用户")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "时长类型：0-默认 1-1天 2-7天 3-30天 4-永久")
    private Integer durationType;

    @Schema(description = "原因类型：0-默认 1-涉黄信息 2-有害信息 3-人身攻击 4-违法信息 5-不实信息 6-违规营销 7-其他")
    private Integer reasonType;

    @Schema(description = "原因内容")
    private String reasonContent;

    @Schema(description = "到期时间")
    private Instant expireTime;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer source;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

}
