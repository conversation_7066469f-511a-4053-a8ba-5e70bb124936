package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * <p>
 * 线下赛问题分组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@TableName("gusto_question_title")
public class OfflineQuestionGroup {

    @Schema(description = "分组ID")
    @TableField("title_id")
    private Long groupId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "状态：0-删除 1-启用 2-未启用")
    private Integer state;

    @Schema(description = "报名时间节点")
    private Instant signTime;

    @Schema(description = "比赛时间节点")
    private Instant gameTime;

    /**
     * 获取当前时间问题节点
     */
    public Integer getQuestionNodeType() {
        var now = Instant.now();
        if (now.isBefore(signTime)) {
            return 1;
        } else if (now.isAfter(signTime) && now.isBefore(gameTime)) {
            return 2;
        } else {
            return 3;
        }
    }

}
