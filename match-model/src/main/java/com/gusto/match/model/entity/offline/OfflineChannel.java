package com.gusto.match.model.entity.offline;

import cn.dev33.satoken.secure.SaSecureUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 线下赛通道
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_channel")
public class OfflineChannel extends BaseTenantEntity {

    private static final long serialVersionUID = -1684890759400866631L;

    @Schema(description = "通道ID")
    @TableId(type = IdType.AUTO)
    private Long channelId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "子标题")
    private String subTitle;

    @Schema(description = "报名设置：0-默认 1-普通报名 2-公益报名")
    private Integer signSetting;

    @Schema(description = "公益报名费，单位元")
    private BigDecimal publicSignAmount;

    @Deprecated
    @Schema(description = "是否允许自选号码")
    private Boolean chooseBibno;

    @Schema(description = "开放时间")
    private Instant startTime;

    @Schema(description = "关闭时间")
    private Instant endTime;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "项目列表")
    @TableField(exist = false)
    private List<OfflineProject> projectList;

    @Schema(description = "报名类型：0-默认 1-先支付后审核不抽签 2-先审核后支付不抽签 3-先支付后审核抽签 4-先审核抽签后支付，创建后不可修改")
    private Integer signType;

    @Schema(description = "支付开放时间")
    private Instant payStartTime;

    @Schema(description = "支付关闭时间")
    private Instant payEndTime;

    @Deprecated(since = "兼容")
    @Schema(description = "是否仅限邀请码报名")
    private Boolean inviteCodeLimit;

    @Schema(description = "是否高校通道")
    private Boolean schoolChannel;

    @Schema(description = "学校列表文件URL")
    private String schoolListUrl;

    @Schema(description = "修改截止时间")
    private Instant modifyEndTime;

    @Schema(description = "审核截止时间")
    private Instant reviewEndTime;

    @Schema(description = "是否开启组队")
    private Boolean team;

    @Schema(description = "人数限制")
    private Integer teamMemberLimit;

    @Schema(description = "项目人数限制")
    private Integer teamProjectMemberLimit;

    @Schema(description = "组队截止时间")
    private Instant teamEndTime;

    @Schema(description = "邀请码填写类型：0-默认 1-正常 2-必填 3-隐藏")
    private Integer inviteCodeInputType;

    @Schema(description = "通道类型：0-默认 1-正常 2-独立报名 3-外籍通道")
    private Integer channelType;

    /**
     * 获取通道验证码
     */
    public String getVerifyCode() {
        return SaSecureUtil.md5BySalt(channelId.toString(), "GUSTO");
    }

}
