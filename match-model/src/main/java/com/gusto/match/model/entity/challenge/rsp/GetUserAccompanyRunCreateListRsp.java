package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取用户陪跑列表响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class GetUserAccompanyRunCreateListRsp {

    @Schema(description = "陪跑列表")
    private List<GetUserCreateAccompanyRunRsp> accompanyList;

    @Schema(description = "项目名称")
    private String projectName;

}
