package com.gusto.match.model.entity.challenge.rsp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gusto.match.model.entity.challenge.dto.AccompanyMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/26
 */
@Data
public class GetAccompanyMemberRsp {

    @Schema(description = "陪跑名单")
    private IPage<AccompanyMemberDTO> accompanyMember;

    @Schema(description = "报名总人数")
    private Integer signCount;

    @Schema(description = "异地人数")
    private Integer differentPlaceCount;

    @Schema(description = "到场人数")
    private Integer presentCount;

    @Schema(description = "状态 0-默认 1-进行中 2-结束")
    private Integer state;

    @Schema(description = "项目名称")
    private String projectName;

}
