package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取教练状态响应
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class GetCoachStateRsp {
    @Schema(description = "是否完成过半马")
    private Boolean finishedHalfMarathon;

    // 用于审核
    @Schema(description = "教练申请审核状态：0-默认 1-待审核 2-通过 3-拒绝")
    private Integer coachApplicationState;

    // 用来提示
    @Schema(description = "判断提示信息是否已经展示")
    private Boolean isShow;

    // 用来提示
    @Schema(description = "是否是新教练")
    private Boolean newCoach;

    // 用来提示
    @Schema(description = "教练等级是否改变")
    private Boolean coachLevelUpChange;

    // 教练等级
    @Schema(description = "教练等级")
    private Integer coachLevel;
}
