package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.shop.dto.ShopGoodsRelationSkuStock;

import java.io.IOException;
import java.util.List;

public class ListShopGoodsRelationSkuStockJsonTypeHandler extends JacksonTypeHandler {

    private static final TypeReference<List<ShopGoodsRelationSkuStock>> typeReference = new TypeReference<>() {
    };

    public ListShopGoodsRelationSkuStockJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
