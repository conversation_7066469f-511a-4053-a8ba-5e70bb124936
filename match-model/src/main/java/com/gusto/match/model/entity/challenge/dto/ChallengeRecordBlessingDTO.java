package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 陪跑祝福
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
public class ChallengeRecordBlessingDTO {

    @Schema(description = "祝福ID")
    private Long blessId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "祝福")
    private String blessing;

    @Schema(description = "挑战者回复")
    private String reply;

    @Schema(description = "创建时间")
    private Long createTime;

}
