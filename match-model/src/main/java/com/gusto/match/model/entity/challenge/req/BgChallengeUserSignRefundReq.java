package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * 退款
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class BgChallengeUserSignRefundReq {

    @Schema(description = "报名ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long signId;

    @Schema(description = "操作类型：0-默认 1-退赛退款（默认全退） 2-仅退款", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer operationType;

    @Schema(description = "需退款的订单明细ID列表，操作类型为1时，全部传过来", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> orderDetailIdList;

    @Schema(description = "退款类型：0-默认 1-系统 2-手动")
    @Positive
    private Integer refundType;

    @Schema(description = "平台退款单号")
    private String refundId = "";

    @Schema(description = "退款账户名")
    private String refundName = "";

    @Schema(description = "退款账户号")
    private String refundAccount = "";

    @Schema(description = "管理员备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String adminRemark;

}
