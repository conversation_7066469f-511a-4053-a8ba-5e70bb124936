package com.gusto.match.model.entity.location.rsp;

import com.gusto.match.model.entity.location.dto.LocationShareMemberItemDTO;
import com.gusto.match.model.entity.location.dto.LocationShareMessageItemDTO;
import com.gusto.match.model.entity.location.dto.LocationShareRoomChangeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取共享成员列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMemberGetShareListRsp {

    @Schema(description = "房间信息，有更新才返回，且仅返回一次，否则为空")
    private LocationShareRoomChangeDTO room;

    @Schema(description = "成员数")
    private Integer memberCount;

    @Schema(description = "共享定位的成员列表")
    private List<LocationShareMemberItemDTO> memberList;

    @Schema(description = "新消息列表")
    private List<LocationShareMessageItemDTO> messageList;

}
