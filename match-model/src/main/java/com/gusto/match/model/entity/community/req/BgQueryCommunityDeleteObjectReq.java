package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取删除列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryCommunityDeleteObjectReq {

    @Schema(description = "话题ID")
    private Long topicId = 0L;

    @Schema(description = "用户昵称")
    private String fromNickname = "";

    @Schema(description = "用户手机")
    private String fromMobile = "";

    @Schema(description = "用户ID")
    private Long fromAuthorId = 0L;

    @Schema(description = "排序类型：0-默认 1-最新 2-点赞最多")
    private Integer sortType = 0;

    @Schema(description = "状态：0-默认 1-展示 2-用户删除 3-管理员删除 4-限流 5-审核不通过")
    private Integer state = 0;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
