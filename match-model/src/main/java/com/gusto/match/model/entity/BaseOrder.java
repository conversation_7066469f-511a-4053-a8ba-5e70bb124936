package com.gusto.match.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <p>
 * 基类-订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseOrder extends BaseEntity {
    private static final long serialVersionUID = 7342063327601658080L;

    @Schema(description = "订单ID")
    @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "电话")
    private String mobile;

    @Schema(description = "订单号")
    private String outTradeNo;

    @Schema(description = "支付单号")
    private String tradeNo;

    @Schema(description = "订单状态：0-默认 1-未支付 2-已支付 3-已发货 4-确认收货 5-已退款 6-已取消 7-已部分退款 8-交易结束")
    private Integer orderState;

    @Schema(description = "支付状态：0-默认 1-未支付 2-已支付")
    private Integer payState;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "支付时间")
    private Instant payTime;

    // enum TradeType
    @Schema(description = "支付类型：1-支付宝WAP 11-微信JSAPI 12-微信H5 13-微信小程序 21-PAYPAL")
    private Integer payType;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "送货地址")
    private String address;

    @Schema(description = "推送状态")
    private Boolean pushState;

    @Schema(description = "快递公司：0-无 1-中通快递 2-顺丰快递 3-京东快递 4-德邦快递 5-邮政快递 6-圆通快递 7-联昊通快递 8-申通快递 9-京广速递")
    private Integer shipChannel;

    @Schema(description = "快递单号")
    private String shipSn;

    @Schema(description = "快递录入时间")
    private Instant shipTime;

    @Schema(description = "用户确认收货时间")
    private Instant confirmTime;

    @Schema(description = "买家备注")
    private String remark;

    @Schema(description = "卖家备注")
    private String adminRemark;

    @Schema(description = "退款备注")
    private String refundRemark;

    @Schema(description = "实际退款金额（有可能退款金额小于实际支付金额）")
    private BigDecimal refundAmount;

    @Schema(description = "退款时间")
    private Instant refundTime;
}
