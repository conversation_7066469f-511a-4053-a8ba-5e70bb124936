package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class TrainingStudentActiveData {
    @Schema(description = "近7天跑步学员数量")
    private Long last7DaysRun;

    @Schema(description = "近14天跑步学员数量")
    private Long last14DaysRun;

    @Schema(description = "近30天跑步学员数量")
    private Long last30DaysRun;

    @Schema(description = "近60天跑步学员数量")
    private Long last60DaysRun;
}
