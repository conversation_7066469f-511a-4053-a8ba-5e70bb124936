package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取完赛证书渲染数据
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeTemplateGetCertMemberRsp {

    @Schema(description = "成员用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "实际贡献距离，单位米")
    private Double distance;

}
