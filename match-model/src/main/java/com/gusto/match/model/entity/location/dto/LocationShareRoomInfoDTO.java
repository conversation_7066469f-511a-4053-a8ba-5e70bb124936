package com.gusto.match.model.entity.location.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareRoomInfoDTO {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "房间号")
    private String number;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "公开状态：0-默认 1-公开 2-不公开")
    private Integer publicState;

    @Schema(description = "是否加入")
    private Boolean join;

    @Schema(description = "状态：0-默认 1-未开始 2-进行中 3-已结束")
    private Integer state;

}
