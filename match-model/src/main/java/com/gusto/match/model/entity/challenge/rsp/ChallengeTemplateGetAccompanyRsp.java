package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取陪跑证书渲染数据
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeTemplateGetAccompanyRsp {

    @Schema(description = "是否可以查看证书")
    private Boolean viewCert;

    @Schema(description = "不能查看证书的原因")
    private String viewCertReason;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "发起者用户ID")
    private Long userId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑者用户ID")
    private Long memberUserId;

    @Schema(description = "参与陪跑证书模板背景图")
    private String accompanyBackgroundImage;

    @Schema(description = "陪跑者姓名")
    private String memberUsername;

    @Schema(description = "发起者姓名")
    private String username;

    @Schema(description = "陪跑里程，单位米")
    private Double memberDistance;

    @Schema(description = "陪跑用时，单位秒")
    private Integer memberDuration;

    @Schema(description = "陪跑日期")
    private String memberDate;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "目标里程，单位米")
    private Double targetDistance;

    @Schema(description = "完赛里程，单位米")
    private Double finishedDistance;

    @Schema(description = "完赛日期")
    private String finishedDate;

    @Schema(description = "今年累计陪跑次数")
    private Integer totalMemberCount;

    @Schema(description = "今年累计陪跑距离，单位米")
    private Double totalMemberDistance;

    @Schema(description = "今年累计陪跑用时，单位秒")
    private Integer totalMemberDuration;

    @Schema(description = "感谢信")
    private String thankLetter;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "证书印章图片")
    private String certStampImage;

    @Schema(description = "是否对外展示目标跑量")
    private Boolean showTargetDistance;

}
