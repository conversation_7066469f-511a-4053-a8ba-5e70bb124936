package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/29
 */
@Data

public class BgSimpleCoachApplication {

    @Schema(description = "教练申请记录ID")
    private Long recordId;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Schema(description = "审核状态：0-默认 1-待审核 2-通过 3-拒绝")
    private Integer state;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "参与的万名教练计划")
    private String joinTenThousandCoaches;

    @Schema(description = "半马以上 PB成绩，单位秒")
    private Long halfMarathonPb;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "自我介绍")
    private String introduce;

    @Schema(description = "申请时间")
    private Long applyTime;
}
