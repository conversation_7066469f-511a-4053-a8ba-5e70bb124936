package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * 报名陪跑请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class JoinUserAccompanyRunReq {

    @Schema(description = "陪跑记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long recordId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String username;

    @Schema(description = "陪跑祝福")
    @Size(max = 200)
    private String blessing;

    @Schema(description = "陪跑方式：0-默认 1-到场陪跑 2-异地陪跑", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer accompanyMode;

    @Schema(description = "总跑团ID")
    private Long headClubId;

}
