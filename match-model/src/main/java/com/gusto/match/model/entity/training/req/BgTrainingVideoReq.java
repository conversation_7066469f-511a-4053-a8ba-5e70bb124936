package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练视频后台更新创建请求类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data

public class BgTrainingVideoReq {

    @Schema(description = "视频ID，更新时必传，等于0时为创建")
    private Long videoId;

    @Schema(description = "封面图", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coverImg;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "类目ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long categoryId;

    @Schema(description = "视频链接", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "视频时长，单位秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long duration;

    @Schema(description = "虚拟播放量")
    private Long fakeView;
}
