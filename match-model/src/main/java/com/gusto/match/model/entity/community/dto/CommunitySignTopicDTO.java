package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class CommunitySignTopicDTO {

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛")
    private Integer primaryCategory;

    @Schema(description = "二级分类：0-默认 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer secondaryCategory;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

}
