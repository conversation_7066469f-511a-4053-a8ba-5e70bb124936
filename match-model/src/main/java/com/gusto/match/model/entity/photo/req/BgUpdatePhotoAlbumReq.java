package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 后台-更新相册请求
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgUpdatePhotoAlbumReq {

    @Schema(description = "相册ID", required = true)
    @NotNull(message = "相册ID不能为空")
    private Long albumId;

    @Schema(description = "相册名称")
    private String albumName;

    @Schema(description = "相册描述")
    private String albumDescription;

    @Schema(description = "谱时相册ID列表")
    private List<String> photoPlusAlbumIds;

    @Schema(description = "摄影师分成比例（百分比，如30表示30%）")
    @DecimalMin(value = "0", message = "摄影师分成比例不能小于0")
    @DecimalMax(value = "100", message = "摄影师分成比例不能大于100")
    private BigDecimal photographerShareRatio;

    @Schema(description = "公司分成比例（百分比，如70表示70%）")
    @DecimalMin(value = "0", message = "公司分成比例不能小于0")
    @DecimalMax(value = "100", message = "公司分成比例不能大于100")
    private BigDecimal companyShareRatio;

    @Schema(description = "单张照片价格（元）")
    @Positive(message = "单张照片价格必须大于0")
    private BigDecimal singlePhotoPrice;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "相册封面图片URL")
    private String coverImageUrl;

    @Schema(description = "排序权重，数值越大排序越靠前")
    private Integer sortWeight;

    @Schema(description = "备注信息")
    private String remark;
}
