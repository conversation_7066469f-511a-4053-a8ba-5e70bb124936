package com.gusto.match.model.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PhotoPlus配置属性
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@Component
@ConfigurationProperties(prefix = "photo.photo-plus")
public class PhotoPlusProperties {

    /**
     * API主机地址
     * 测试环境: https://testwxplus.plusx.cn
     * 正式环境: https://live.photoplus.cn
     */
    private String apiHost = "";

    /**
     * 加密盐值
     * 测试环境默认为: adui
     */
    private String salt = "";

}
