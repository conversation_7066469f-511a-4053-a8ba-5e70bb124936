package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练数据
 *
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class TrainingData {
    @Schema(description = "教练人数")
    private Long coachCount;

    @Schema(description = "学员人数")
    private Long studentCount;

    @Schema(description = "视频播放人数")
    private Long videoViewCount;

    @Schema(description = "文章阅读次数")
    private Long articleViewCount;

    @Schema(description = "装备评测阅读次数")
    private Long reviewViewCount;
}
