package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseUserSign;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 赛事-用户报名
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "match_user_sign", autoResultMap = true)
public class UserSign extends BaseUserSign {

    private static final long serialVersionUID = -2758209049515473690L;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "号码前缀")
    private String bibnoSub;

    @Schema(description = "号码数字长度")
    private Integer bibnoLength;

    @Schema(description = "号码")
    private Long bibno;

    @Schema(description = "报名卡ID")
    private Long userCardId;

    @Schema(description = "是否修改过号码")
    private Boolean modifyBibno;

    @Schema(description = "完赛状态：0-默认 1-未挑战 2-挑战成功 3-挑战失败")
    private Integer finishedState;

    @Schema(description = "完赛时间")
    private Instant finishedTime;

    @Schema(description = "购买订单序号")
    private Integer signPackageIndex;

    @Schema(description = "挑战地点名称")
    private String challengeLocation;

    @Schema(description = "报名套餐名称")
    private String signPackageName;

    @Schema(description = "报名套餐商品ID列表")
    @TableField(typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> signPackageGoodsIds;

    @Schema(description = "挑战时间")
    private Instant challengeTime;

    @Schema(description = "挑战地点坐标")
    private String challengeCoordinate;

    @Schema(description = "来源渠道：0-默认 1-微信 2-APP 3-其他 4-小程序")
    private Integer sourceChannel;

    @Schema(description = "来源类型：0-直接报名 1-微信分享 2-APP分享 3-公众号 4-弹窗 5-私域 6-社群 7-推送 8-外部")
    private Integer sourceType;

    @Schema(description = "来源ID")
    private String sourceId;

    @Schema(description = "报名状态：0-默认 1-未报名 2-报名未支付 3-报名已支付 4-报名已取消")
    private Integer signState;

    @Schema(description = "管理员备注")
    private String adminRemark;

    @Schema(description = "退赛退款状态：0-默认 1-已申请 2-已退款 3-退款中 4-退款失败")
    private Integer refundState;

    @Schema(description = "退赛申请时间")
    private Instant refundApplyTime;

    @Schema(description = "退赛申请来源：0-默认 1-用户 2-管理员")
    private Integer refundApplySource;

    @Schema(description = "退款账户名")
    private String refundName;

    @Schema(description = "退款账户号")
    private String refundAccount;

    @Schema(description = "退款时间")
    private Instant refundTime;

    @Schema(description = "挑战距离，仅年年十八可以设置")
    private Double challengeDistance;

    /**
     * 完整号码
     */
    public String getFullBibno() {
        return bibnoSub + String.format("%0" + bibnoLength + "d", bibno);
    }

    /**
     * 计算目标距离
     */
    public Double calTargetDistance(Project project) {
        switch (project.getTargetType()) {
            case 1:
                return 100000.0 - getAge() * 1000.0;
            case 2:
                return getAge() * 1000.0;
            case 0:
                if (challengeDistance == null || challengeDistance == 0.0) {
                    return project.getTargetDistance();
                } else {
                    return challengeDistance;
                }
            default:
                return 0.0;
        }
    }

}
