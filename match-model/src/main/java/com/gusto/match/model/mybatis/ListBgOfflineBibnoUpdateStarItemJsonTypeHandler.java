package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.offline.rsp.BgOfflineBibnoUpdateStarItem;

import java.io.IOException;
import java.util.List;

/**
 * eg: <result column="role_ids" property="roleIds" jdbcType="VARCHAR" typeHandler="com.gusto.match.model.mybatis
 * .ListLongJsonTypeHandler"/>
 * <p>
 * ps: 记得 @TableName(autoResultMap = true)
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public class ListBgOfflineBibnoUpdateStarItemJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<BgOfflineBibnoUpdateStarItem>> typeReference = new TypeReference<>() {
    };

    public ListBgOfflineBibnoUpdateStarItemJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
