package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设置学员备注请求
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data

public class UpdateStudentNoteReq {
    @Schema(description = "学员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long studentId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String note;
}
