package com.gusto.match.model.entity.challenge.rsp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/1
 */
@Data
public class GetBlessingDetailRsp {

    @Schema(description = "祝福列表")
    private IPage<GetBlessingElementDetailRsp> blessingDetail;

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "赛事名称")
    private String matchName;

    @Schema(description = "背景图")
    private String backgroundImage;
}
