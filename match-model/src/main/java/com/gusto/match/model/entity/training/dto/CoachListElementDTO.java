package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 教练列表子项
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class CoachListElementDTO {
    @Schema(description = "教练ID")
    private Integer coachId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "教练等级：0-默认 1-准教练 2-初级教练 3-中级教练 4-高级教练")
    private Integer level;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "学员人数")
    private Integer studentCount;
}
