package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.entity.offline.rsp.BatchImportOfflineUserSignItem;
import com.gusto.match.model.mybatis.ListBatchImportOfflineUserSignItemJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 线下赛用户报名导入任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_sign_import_job", autoResultMap = true)
public class OfflineUserSignImportJob extends BaseTenantEntity {

    private static final long serialVersionUID = -4285777784858207299L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "导入总数")
    private Integer totalCount;

    @Schema(description = "成功导入的数量")
    private Integer successCount;

    @Schema(description = "导入失败的报名列表")
    @TableField(typeHandler = ListBatchImportOfflineUserSignItemJsonTypeHandler.class)
    private List<BatchImportOfflineUserSignItem> errorSignList;

}
