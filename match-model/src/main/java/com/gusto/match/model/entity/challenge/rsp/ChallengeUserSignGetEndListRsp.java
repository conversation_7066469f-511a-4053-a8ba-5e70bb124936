package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取用户报名列表
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
public class ChallengeUserSignGetEndListRsp {

    @Schema(description = "挑战记录列表")
    private List<ChallengeUserSignGetEndListItemRsp> itemList;

    @Schema(description = "当前赛季别名")
    private String seasonAlias;

    @Schema(description = "当前赛季头图-挑战成功")
    private String headImageForSuccess;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "赛季名称")
    private String seasonName;

}
