package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 教练
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class CoachDTO {
    @Schema(description = "教练ID")
    private Integer coachId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "是否进行过教程演示")
    private Boolean showTutorial;

    @Schema(description = "获奖经历")
    private List<String> awards;

    @Schema(description = "相关资质")
    private List<String> qualifications;

    @Schema(description = "是否隐藏自己的训练数据")
    private Boolean isHide;

    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "参与的万名教练计划")
    private String joinTenThousandCoaches;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;
}
