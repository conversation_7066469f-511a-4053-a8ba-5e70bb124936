package com.gusto.match.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 基类-App跳转响应
 *
 * <AUTHOR>
 * @since 2022-02-15
 */
@Data
public class BaseAppJump implements Serializable {

    private static final long serialVersionUID = 6210940155566082111L;

    @Schema(description = "跳转类型：0-默认 1-H5 2-APP 3-小程序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer jumpType;

    @Schema(description = "跳转内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String jumpContent;

}
