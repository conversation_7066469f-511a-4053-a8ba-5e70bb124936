package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 隐藏
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class BgHideCommunityObjectReq {

    @Schema(description = "对象类型：0-默认 1-动态 2-评论", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer objectType;

    @Schema(description = "对象ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long objectId;

}
