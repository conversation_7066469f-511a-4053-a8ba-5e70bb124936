package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 线下赛邀请码创建任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_invite_code_create_job", autoResultMap = true)
public class OfflineInviteCodeCreateJob extends BaseTenantEntity {

    private static final long serialVersionUID = 3423715553327013375L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID，不填默认不限项目")
    private Long projectId;

    @Schema(description = "邀请码类型：0-默认 1-折扣 2-实际支付 3-优惠金额")
    private Integer codeType;

    @Schema(description = "邀请码个数")
    private Integer codeCount;

    @Schema(description = "优惠金额")
    private BigDecimal discount;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "是否受报名名额限制")
    private Boolean signLimit;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "赛事类型限制：0-不限制 1-马拉松 2-越野赛 3-铁人三项 4-徒步 5-欢乐跑 6-骑行")
    private Integer raceTypeLimit;

    @Schema(description = "证件号码列表")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> cardNumberList;

}
