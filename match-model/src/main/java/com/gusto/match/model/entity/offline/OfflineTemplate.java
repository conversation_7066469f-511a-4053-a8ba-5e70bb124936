package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.mybatis.ListOfflineTemplateFieldJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 线下赛模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_template", autoResultMap = true)
public class OfflineTemplate extends BaseEntity {

    private static final long serialVersionUID = 6485303335014407220L;

    @Schema(description = "模板ID")
    @TableId(type = IdType.AUTO)
    private Long templateId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "模板类型：0-默认 1-证书模板 2-号码布模板")
    private Integer templateType;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "高")
    private Long height;

    @Schema(description = "宽")
    private Long width;

    @Schema(description = "字段列表")
    @TableField(typeHandler = ListOfflineTemplateFieldJsonTypeHandler.class)
    private List<OfflineTemplateField> fieldList;

}
