package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2021/9/9
 */
@Data

public class UserInfoDTO {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "学员人数")
    private Long studentCount;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;

    @Schema(description = "粉丝")
    private Long fans;

    @Schema(description = "关注")
    private Long follows;

    @Schema(description = "背景图")
    private String bgImg;

    @Schema(description = "是否被关注 0-默认 1-关注 2-没有关注")
    private Integer attention;

    @Schema(description = "使用用户对资料用户申请状态 0-默认 1-申请中 2-申请成功 3-申请失败")
    private Integer applicationStatus;

    @Schema(description = "资料用户对使用用户申请状态 0-默认 1-申请中 2-申请成功 3-申请失败")
    private Integer toUserApplicationStatus;

    @Schema(description = "查看用户是否是教练")
    private Boolean coach;

    @Schema(description = "是否隐藏训练数据")
    private Boolean hide;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "查看用户与你的状态 0-默认 1-是你的学员 2-是你的教练")
    private Integer viewUserState;

    // V1.2
    @Schema(description = "教练等级 0-默认 1-准教练 2-初级教练 3-中级教练 4-高级教练")
    private Integer level;

    // v1.3
    @Schema(description = "是否有认证证书")
    private Boolean haveCert;
}
