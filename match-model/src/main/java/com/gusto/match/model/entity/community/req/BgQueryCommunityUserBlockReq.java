package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取用户禁言列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryCommunityUserBlockReq {

    @Schema(description = "话题ID")
    private Long topicId = 0L;

    @Schema(description = "用户昵称")
    private String fromNickname = "";

    @Schema(description = "用户手机")
    private String fromMobile = "";

    @Schema(description = "用户ID")
    private Long fromAuthorId = 0L;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
