package com.gusto.match.model.entity.location.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareRoomGetDetailRsp {

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "房间号")
    private String number;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "成员人数限制，为0表示无限制")
    private Integer memberLimit;

    @Schema(description = "共享人数限制，为0表示无限制")
    private Integer shareLimit;

    @Schema(description = "公开状态：0-默认 1-公开 2-不公开")
    private Integer publicState;

    @Schema(description = "验证码")
    private String verifyCode;

    @Schema(description = "轨迹名称")
    private String trackName = "";

    @Schema(description = "轨迹链接")
    private String trackUrl;

    @Schema(description = "轨迹起点")
    private String trackStart = "";

    @Schema(description = "轨迹终点")
    private String trackEnd = "";

    @Schema(description = "轨迹距离，单位米")
    private Double trackDistance = 0.0;

    @Schema(description = "轨迹点位数")
    private Integer trackPointCount = 0;

    @Schema(description = "共享状态：0-默认 1-共享 2-不共享")
    private Integer shareState;

    @Schema(description = "成员数")
    private Integer memberCount;

    @Schema(description = "状态：0-默认 1-未开始 2-进行中 3-已结束")
    private Integer state;

}
