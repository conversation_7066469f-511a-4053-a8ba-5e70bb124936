package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户陪跑成员-跑步记录关联
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("match_user_accompany_run_member_relation")
public class UserAccompanyRunMemberRelation extends BaseEntity {
    private static final long serialVersionUID = -6196223881865513142L;

    @Schema(description = "关联ID")
    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;
}
