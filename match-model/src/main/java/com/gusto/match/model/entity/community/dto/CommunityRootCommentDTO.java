package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 社区评论
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class CommunityRootCommentDTO {

    @Schema(description = "评论ID")
    private Long commentId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "发表评论的作者类型：0-默认 1-用户 2-官方")
    private Integer fromAuthorType;

    @Schema(description = "发表评论的作者ID")
    private Long fromAuthorId;

    @Schema(description = "发表评论的用户昵称")
    private String fromNickname;

    @Schema(description = "发表评论的用户头像")
    private String fromAvatar;

    @Schema(description = "评论对象的作者类型：0-默认 1-用户 2-官方")
    private Integer toAuthorType;

    @Schema(description = "评论对象的作者ID")
    private Long toAuthorId;

    @Schema(description = "评论对象的用户昵称")
    private String toNickname;

    @Schema(description = "评论对象的用户头像")
    private String toAvatar;

    @Schema(description = "根评论ID")
    private Long rootCommentId;

    @Schema(description = "回复评论ID")
    private Long toCommentId;

    @Schema(description = "内容，限制300字")
    private String content;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "评论时间")
    private Long commentTime;

    @Schema(description = "评论列表")
    private List<CommunityCommentDTO> commentList;

    @Schema(description = "是否已点赞")
    private Boolean like;

}
