package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台训练视频 DTO类
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class BgTrainingVideoDTO {

    @Schema(description = "视频ID")
    private Long videoId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "视频链接")
    private String url;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "发布时间，时间戳，单位毫秒")
    private Long postTime;

    @Schema(description = "播放时长，单位秒")
    private Long duration;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "播放次数")
    private Long view;

    @Schema(description = "虚拟浏览量")
    private Long fakeView;

    @Schema(description = "播完次数")
    private Long playCount;

    @Schema(description = "是否置顶，false=不置顶，true=置顶")
    private Boolean top;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;
}
