package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台视频数据（或文章数据）请求类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data

public class BgTrainingKnowledgeDataReq {

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "排序字段：0-默认 1-文章浏览次数或视频播放次数 2-文章分享次数 3-完整播放次数")
    private Integer eventType;

    @Schema(description = "是否升序：true-升序，false-降序")
    private Boolean isAsc;
}
