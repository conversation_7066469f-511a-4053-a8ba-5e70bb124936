package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * <p>
 * 根据分类分页获取话题列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommunityTopicPageByCategoryReq {

    @Parameter(description = "分类：0-默认 101-线下赛 102-线上赛 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer category = 0;

    @Parameter(description = "current")
    private Long current = 1L;

    @Parameter(description = "size")
    private Long size = 20L;

}
