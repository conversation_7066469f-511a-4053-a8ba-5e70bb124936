package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/10
 */
@Data

public class NewTrainingActivityDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区")
    private String area;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;
}
