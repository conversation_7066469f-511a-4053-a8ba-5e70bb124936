package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.challenge.dto.ChallengeRecordBlessingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取陪跑祝福列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
public class ChallengeUserSignGetBlessingListRsp {

    @Schema(description = "祝福列表")
    private List<ChallengeRecordBlessingDTO> blessingList;

    @Schema(description = "项目名称")
    private String projectName;

}
