package com.gusto.match.model.entity.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
public class PayPalToken implements Serializable {

    private static final long serialVersionUID = -9204311949805675238L;

    @Schema(description = "accessToken")
    private String accessToken;

    @Schema(description = "过期时间")
    private Long expiresTime;

}
