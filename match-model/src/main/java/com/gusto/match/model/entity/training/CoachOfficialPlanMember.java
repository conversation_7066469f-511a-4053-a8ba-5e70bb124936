package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 万名教练计划成员
 *
 * <AUTHOR>
 * @since 2021/8/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coach_official_plan_member")
public class CoachOfficialPlanMember extends BaseEntity {
    private static final long serialVersionUID = 7930205473349314429L;

    @TableId(value = "member_id", type = IdType.AUTO)
    @Schema(description = "记录ID")
    private Long memberId;

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "参加期数")
    private String coachProgram;
}
