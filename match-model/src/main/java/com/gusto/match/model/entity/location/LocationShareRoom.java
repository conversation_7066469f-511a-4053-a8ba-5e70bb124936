package com.gusto.match.model.entity.location;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 定位共享房间
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "location_share_room")
public class LocationShareRoom extends BaseEntity {

    private static final long serialVersionUID = 8049907793647769166L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "房间号")
    private String number;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "成员人数限制，为0表示无限制")
    private Integer memberLimit;

    @Schema(description = "共享人数限制，为0表示无限制")
    private Integer shareLimit;

    @Schema(description = "公开状态：0-默认 1-公开 2-不公开")
    private Integer publicState;

    @Schema(description = "验证码")
    private String verifyCode;

    @Schema(description = "轨迹名称")
    private String trackName;

    @Schema(description = "轨迹链接")
    private String trackUrl;

    @Schema(description = "轨迹起点")
    private String trackStart;

    @Schema(description = "轨迹终点")
    private String trackEnd;

    @Schema(description = "轨迹距离，单位米")
    private Double trackDistance;

    @Schema(description = "轨迹点位数")
    private Integer trackPointCount;

    @Schema(description = "点击数")
    private Integer clickRoomCount;

    @Schema(description = "分享数")
    private Integer shareRoomCount;

    @Schema(description = "成员数")
    private Integer memberCount;

    @Schema(description = "当前成员数")
    private Integer currentMemberCount;

    @Schema(description = "分享定位数")
    private Integer shareLocationCount;

    @Schema(description = "最近更新时间")
    private Instant lastUpdateTime;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    /**
     * 根据时间计算状态
     */
    public Integer getState() {
        var now = Instant.now();
        if (now.isBefore(startTime)) {
            return 1;
        }
        if (now.isAfter(endTime)) {
            return 3;
        }
        return 2;
    }

}
