package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 创建版主
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class CommunityAdminCreateReq {

    @Parameter(description = "话题ID", required = true)
    @Positive
    private Long topicId;

    @Parameter(description = "手机号，和用户ID二选一")
    private String mobile;

    @Parameter(description = "用户ID，和手机号二选一")
    private Long userId;

}
