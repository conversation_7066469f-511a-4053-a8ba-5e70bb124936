package com.gusto.match.model.entity.community.rsp;

import com.gusto.match.model.entity.community.dto.CommunityTagDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取官方动态详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class BgGetCommunityOfficialPostDetailRsp {

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "标题，限制20字")
    private String title;

    @Schema(description = "内容，限制1000字")
    private String content;

    @Schema(description = "图片列表，限制9张")
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

}
