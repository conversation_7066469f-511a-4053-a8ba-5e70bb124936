package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.entity.offline.rsp.BatchImportOfflineUserSignRefundItem;
import com.gusto.match.model.mybatis.ListBatchImportOfflineUserSignRefundItemJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 线下赛用户报名导入退款任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_sign_import_refund_job", autoResultMap = true)
public class OfflineUserSignImportRefundJob extends BaseTenantEntity {

    private static final long serialVersionUID = 1079944026052020099L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "退款记录类型：0-默认 1-退赛 4-不中签 5-审核不通过 6-退差价")
    private Integer recordType;

    @Schema(description = "退款类型：0-默认 1-系统 2-手动")
    private Integer refundType;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "导入总数")
    private Integer totalCount;

    @Schema(description = "导入失败的记录列表")
    @TableField(typeHandler = ListBatchImportOfflineUserSignRefundItemJsonTypeHandler.class)
    private List<BatchImportOfflineUserSignRefundItem> errorRecordList;

    @Schema(description = "管理员ID")
    private Long adminId;

    @Schema(description = "管理员名称")
    private String adminName;

    @Schema(description = "管理员IP")
    private String adminIp;

}
