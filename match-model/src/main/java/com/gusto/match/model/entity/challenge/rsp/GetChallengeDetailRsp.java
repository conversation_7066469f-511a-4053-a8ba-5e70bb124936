package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.challenge.dto.ChallengeUserSignDTO;
import com.gusto.match.model.entity.challenge.dto.ProjectNameDTO;
import com.gusto.match.model.entity.challenge.dto.SeasonNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class GetChallengeDetailRsp {

    @Schema(description = "挑战赛 ID")
    private Long matchId;

    @Schema(description = "赛季 ID")
    private Long seasonId;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "赛季名称列表")
    private List<SeasonNameDTO> seasonNameList;

    @Schema(description = "项目名称列表")
    private List<ProjectNameDTO> projectNameList;

    @Schema(description = "报名人数")
    private Integer signCount;

    @Schema(description = "报名开始时间")
    private Long signStartTime;

    @Schema(description = "报名截止时间")
    private Long signEndTime;

    @Schema(description = "赛事介绍")
    private String detailImage;

    @Schema(description = "规则说明")
    private String rule;

    @Schema(description = "常见问题")
    private String question;

    @Schema(description = "我的挑战记录列表，已登录时返回")
    private List<ChallengeUserSignDTO> signList;

    @Schema(description = "最近参与的陪跑昵称，已登录时返回")
    private String accompanyNickname;

    @Schema(description = "最近参与的陪跑里程，已登录时返回")
    private Double accompanyDistance;

    @Schema(description = "最近参与的陪跑结束时间，已登录时返回")
    private Long accompanyEndTime;

    @Schema(description = "用户是否提交报名，已登录时返回")
    private Boolean userSign;

    @Schema(description = "报名ID，已登录时返回")
    private Long signId;

    @Schema(description = "用户是否支付，已登录时返回")
    private Boolean pay;

    @Schema(description = "当前赛季名称")
    private String currentSeasonName;

}
