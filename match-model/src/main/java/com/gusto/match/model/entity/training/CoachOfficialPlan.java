package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 万名教练计划
 *
 * <AUTHOR>
 * @since 2021/8/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coach_official_plan")
public class CoachOfficialPlan extends BaseEntity {
    private static final long serialVersionUID = 6184839379592186956L;

    @TableId(value = "plan_id", type = IdType.AUTO)
    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "期号")
    private Long number;

    //V1.3
    @Schema(description = "课程开始时间")
    private Instant classStartTime;

    @Schema(description = "课程结束时间")
    private Instant classEndTime;
}
