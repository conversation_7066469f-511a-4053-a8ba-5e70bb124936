package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 训练文章
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach_training_article")
public class TrainingArticle extends BaseEntity implements Deletable {
    private static final long serialVersionUID = 8137430684627671346L;

    @TableId(value = "article_id", type = IdType.AUTO)
    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章链接")
    private String url;

    @Schema(description = "文章分类：0-默认 1-文章知识 2-装备测评")
    private Integer type;

    @Schema(description = "发布时间")
    private Instant postTime;

    @Schema(description = "真实浏览量")
    private Long view;

    @Schema(description = "分享次数")
    private Long shareCount;

    @Schema(description = "置顶")
    private Boolean top;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
