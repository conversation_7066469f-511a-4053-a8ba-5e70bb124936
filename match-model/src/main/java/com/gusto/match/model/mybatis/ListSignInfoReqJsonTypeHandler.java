package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.training.req.SignInfoReq;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/26
 */
public class ListSignInfoReqJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<SignInfoReq>> typeReference = new TypeReference<>() {
    };

    public ListSignInfoReqJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
