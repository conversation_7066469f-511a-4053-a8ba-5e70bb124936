package com.gusto.match.model.entity.sunshine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-用户陪跑成员
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data

public class SunshineBgUserAccompanyRunMemberDTO {
    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "排序序号，默认为0，越大越靠前")
    private Integer sort = 0;

    @Schema(description = "报名时间")
    private Long signTime;

}
