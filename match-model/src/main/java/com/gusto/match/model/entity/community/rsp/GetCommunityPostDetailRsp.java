package com.gusto.match.model.entity.community.rsp;

import com.gusto.match.model.entity.community.dto.CommunityTagDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取动态详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class GetCommunityPostDetailRsp {

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "话题名称")
    private String topicName;

    @Schema(description = "作者类型：0-默认 1-用户 2-官方")
    private Integer authorType;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "标题，限制20字")
    private String title;

    @Schema(description = "内容，限制1000字")
    private String content;

    @Schema(description = "图片列表，限制9张")
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer activityType;

    @Schema(description = "总距离，单位米")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    private Integer totalDuration;

    @Schema(description = "设备")
    private String deviceModel;

    @Schema(description = "运动天数")
    private Integer runDayCount;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "发布时间")
    private Long postTime;

    @Schema(description = "最后修改时间")
    private Long lastUpdateTime;

    @Schema(description = "关注状态：0-默认 1-本人 2-未关注 3-已关注")
    private Integer followState;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

    @Schema(description = "是否点赞")
    private Boolean like;

    @Schema(description = "是否管理员")
    private Boolean admin;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主")
    private Integer adminType;

    @Schema(description = "是否报名")
    private Boolean sign;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛")
    private Integer primaryCategory;

}
