package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/17
 */
@Data

public class GetCoachApplicationRsp {
    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "开始跑步时间")
    private Long runStartTime;

    @Schema(description = "自我介绍")
    private String introduce;

    @Schema(description = "教练编号")
    private String coachNumber;
}
