package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取学员汇总信息响应
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class GetStudentSummaryRsp {
    @Schema(description = "学员总数")
    private Long totalCount;

    @Schema(description = "本月新增学员")
    private Long currentMonthNewCount;

    @Schema(description = "近15天跑步人数")
    private Long lastHalfMonthRunCount;

    @Schema(description = "近30天跑步人数")
    private Long lastMonthRunCount;
}
