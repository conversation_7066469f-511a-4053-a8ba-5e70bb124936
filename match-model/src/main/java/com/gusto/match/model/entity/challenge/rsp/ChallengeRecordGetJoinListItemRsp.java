package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取用户参与的陪跑记录列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class ChallengeRecordGetJoinListItemRsp {

    @Schema(description = "陪跑列表")
    private List<ChallengeRecordGetJoinListItemMember> memberList;

    @Schema(description = "今年累计陪跑次数")
    private Integer totalMemberCount;

    @Schema(description = "今年累计陪跑距离，单位米")
    private Double totalMemberDistance;

    @Schema(description = "今年累计陪跑用时，单位秒")
    private Integer totalMemberDuration;

    @Schema(description = "今年累计陪跑到场次数")
    private Integer totalAttendCount;

    @Schema(description = "今年累计陪跑异地次数")
    private Integer totalRemoteCount;

    @Schema(description = "赛季名称")
    private String seasonName;

}
