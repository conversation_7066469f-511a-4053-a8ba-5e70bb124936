package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练文章后台更新创建请求类
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data

public class BgTrainingArticleReq {

    @Schema(description = "文章ID，更新时必传，等于0时为创建")
    private Long articleId;

    @Schema(description = "文章类型，1=文章知识，2=装备评测", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "文章链接")
    private String url;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章封面")
    private String coverImg;

}
