package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 教练个人主页
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class CoachProfileDTO {
    @Schema(description = "教练ID")
    private Integer coachId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "是否展现自己的训练数据")
    private Boolean hide;

    @Schema(description = "获奖经历")
    private List<String> awards;

    @Schema(description = "相关资质")
    private List<String> qualifications;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;
}
