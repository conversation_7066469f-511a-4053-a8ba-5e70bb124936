package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛队伍
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_team")
public class OfflineTeam extends BaseTenantEntity {

    private static final long serialVersionUID = -8005582937086325986L;

    @Schema(description = "队伍ID")
    @TableId(type = IdType.AUTO)
    private Long teamId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "队长报名ID")
    private Long captainSignId;

    @Schema(description = "名称")
    private String name;

}
