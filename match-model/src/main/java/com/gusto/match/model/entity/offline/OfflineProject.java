package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListOfflineProjectAdditionJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 线下赛项目
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_project", autoResultMap = true)
public class OfflineProject extends BaseTenantEntity {

    private static final long serialVersionUID = 8041987258383929598L;

    @Schema(description = "项目ID")
    @TableId(type = IdType.AUTO)
    private Long projectId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "距离，单位米")
    private Double distance;

    @Schema(description = "报名费，单位元")
    private BigDecimal signAmount;

    @Schema(description = "名额限制，单位人")
    private Integer signLimit;

    @Schema(description = "最小年龄限制")
    private Integer minAgeLimit;

    @Schema(description = "最大年龄限制")
    private Integer maxAgeLimit;

    @Schema(description = "是否需要监护人签字")
    private Boolean parentSign;

    @Schema(description = "附加信息列表")
    @TableField(typeHandler = ListOfflineProjectAdditionJsonTypeHandler.class)
    private List<OfflineProjectAddition> additionList;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "证书模板ID")
    private Long certTemplateId;

    @Schema(description = "是否开放证书")
    private Boolean openCert;

    @Schema(description = "证书开始时间")
    private Instant certStartTime;

    @Deprecated(since = "兼容")
    @Schema(description = "证书结束时间")
    private Instant certEndTime;

    @Schema(description = "号码布模板ID")
    private Long bibnoTemplateId;

    @Schema(description = "是否开放号码布")
    private Boolean openBibno;

    @Schema(description = "号码布开始时间")
    private Instant bibnoStartTime;

    @Deprecated(since = "兼容")
    @Schema(description = "号码布结束时间")
    private Instant bibnoEndTime;

    @Schema(description = "是否需要导入成绩")
    private Boolean importResult;

    @Schema(description = "号码规则ID")
    private Long bibnoRuleId;

    @Schema(description = "是否开启组队")
    private Boolean team;

    @Schema(description = "人数限制")
    private Integer teamMemberLimit;

    @Schema(description = "男人数限制")
    private Integer teamManLimit;

    @Schema(description = "女人数限制")
    private Integer teamWomenLimit;

    @Schema(description = "组队截止时间")
    private Instant teamEndTime;

    @Schema(description = "早鸟价，单位元")
    private BigDecimal specialSignAmount;

    @Schema(description = "早鸟价截止时间")
    private Instant specialSignAmountEndTime;

    @Schema(description = "是否启用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable = true;

    @Schema(description = "是否允许退赛退款")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean allowRefund = true;

    @Schema(description = "是否多人合并报名")
    private Boolean multiSign;

    @Schema(description = "多人合并报名-成人数")
    private Integer multiSignAdultCount;

    @Schema(description = "多人合并报名-儿童数")
    private Integer multiSignChildCount;

    /**
     * 获取当前报名费
     */
    public BigDecimal getCurrentSignAmount() {
        if (specialSignAmountEndTime != null &&
            specialSignAmountEndTime.isAfter(Instant.now()) &&
            specialSignAmount.compareTo(BigDecimal.ZERO) > 0
        ) {
            return specialSignAmount;
        }
        return signAmount;
    }

}
