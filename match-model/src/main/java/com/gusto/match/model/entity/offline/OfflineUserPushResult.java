package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <p>
 * 线下赛用户推送成绩 TODO offline add sql
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_push_result", autoResultMap = true)
public class OfflineUserPushResult extends BaseTenantEntity {

    private static final long serialVersionUID = -8512333793958216938L;

    // 报名信息

    @Schema(description = "成绩ID")
    @TableId(type = IdType.AUTO)
    private Long resultId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "性别：0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "报名ID")
    private Long signId;

    // 成绩信息

    @Schema(description = "净成绩，单位秒")
    private Long pureDuration;

    @Schema(description = "枪声成绩，单位秒")
    private Long gunDuration;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "净分段用时列表，<分段点，分段用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> purePartTimeDurationList;

    @Schema(description = "枪声分段用时列表，<分段点，分段用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> gunPartTimeDurationList;

    @Schema(description = "净累计用时列表，<分段点，累计用时/秒>")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> pureAddTimeDurationList;

    // 第三方信息

    @Schema(description = "第三方ID")
    private Long thirdId;

}
