package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 线下赛计时
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_timing")
public class OfflineTiming extends BaseTenantEntity {

    private static final long serialVersionUID = -5200096102842616666L;

    @Schema(description = "计时ID")
    @TableId(type = IdType.AUTO)
    private Long timingId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "赛虎赛事ID")
    private Long tigerRaceId;

    @Schema(description = "赛虎伙伴代码")
    private String tigerPartnerCode;

    @Schema(description = "赛虎TOKEN")
    private String tigerToken;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "是否启用")
    private Boolean enabled;

}
