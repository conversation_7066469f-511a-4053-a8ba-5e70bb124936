package com.gusto.match.model.entity.community.rsp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gusto.match.model.entity.app.dto.AppBannerDTO;
import com.gusto.match.model.entity.community.dto.CommunityPostDTO;
import com.gusto.match.model.entity.community.dto.CommunityTagDTO;
import com.gusto.match.model.entity.offline.dto.OfflineRouteDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取话题详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class GetCommunityTopicDetailRsp {

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "背景色")
    private String backgroundColor;

    @Schema(description = "一级分类：0-默认 101-线下赛 102-线上赛")
    private Integer primaryCategory;

    @Schema(description = "二级分类：0-默认 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer secondaryCategory;

    @Schema(description = "浏览量")
    private Integer clickCount;

    @Schema(description = "Banner列表")
    private List<AppBannerDTO> bannerList;

    @Schema(description = "标签列表")
    private List<CommunityTagDTO> tagList;

    @Schema(description = "官方动态分页列表")
    private IPage<CommunityPostDTO> officialPostPage;

    @Schema(description = "动态分页列表")
    private IPage<CommunityPostDTO> postPage;

    @Schema(description = "是否已报名")
    private Boolean sign;

    @Schema(description = "是否管理员")
    private Boolean admin;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主")
    private Integer adminType;

    @Schema(description = "展示报名截止时间")
    private Long showSignEndTime;

    @Schema(description = "问题入口ID")
    private Long questionGroupId;

    @Schema(description = "线下赛路线列表")
    private List<OfflineRouteDTO> offlineRouteList;

    @Schema(description = "是否开放排行榜")
    private Boolean openRanking;

    @Schema(description = "赛事开始时间")
    private Long raceStartTime;

    @Schema(description = "赛事截止时间")
    private Long raceEndTime;

}
