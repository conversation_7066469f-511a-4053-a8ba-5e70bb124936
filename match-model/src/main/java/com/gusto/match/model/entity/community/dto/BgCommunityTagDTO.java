package com.gusto.match.model.entity.community.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgCommunityTagDTO {

    @Schema(description = "标签ID，更新时必填")
    private Long tagId = 0L;

    @Schema(description = "话题ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    @Positive
    private Long topicId;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String name;

    @Schema(description = "排序，降序")
    private Integer sort = 0;

    @Schema(description = "发布设置：0-默认 1-立即发布 2-暂不发布", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer publishSetting;

}
