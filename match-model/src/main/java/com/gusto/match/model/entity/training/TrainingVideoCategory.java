package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.entity.Deletable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 训练视频类目
 *
 * <AUTHOR>
 * @since 2022-4-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach_training_video_category")
public class TrainingVideoCategory extends BaseEntity implements Deletable {

    private static final long serialVersionUID = -2046411227248896465L;

    @TableId(value = "category_id", type = IdType.AUTO)
    @Schema(description = "视频类目ID")
    private Long categoryId;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "逻辑删除")
    private boolean deleted;

    @Override
    public void deleted(Instant time) {
        deleted = true;
        setUpdateTime(time);
    }
}
