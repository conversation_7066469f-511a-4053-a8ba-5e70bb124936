package com.gusto.match.model.entity.sunshine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-用户陪跑记录
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data

public class SunshineBgUserAccompanyRunDTO {
    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "陪跑团贡献距离")
    private Double totalDistance;
}
