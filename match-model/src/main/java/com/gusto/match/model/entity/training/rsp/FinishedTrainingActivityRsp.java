package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练活动完成弹窗响应
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Data

public class FinishedTrainingActivityRsp {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "训练活动名称")
    private String activityName;

    @Schema(description = "训练活动开始时间")
    private Long startTime;

    @Schema(description = "训练活动结束时间")
    private Long endTime;

    @Schema(description = "训练活动参与时间")
    private Long joinTime;
}
