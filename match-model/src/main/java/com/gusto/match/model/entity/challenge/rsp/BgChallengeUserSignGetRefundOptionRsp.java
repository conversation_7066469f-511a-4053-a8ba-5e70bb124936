package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.shop.ShopGoodsSpecValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 挑战赛用户报名获取退款选项列表
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
public class BgChallengeUserSignGetRefundOptionRsp {

    @Schema(description = "明细ID")
    private Long detailId;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "商品ID，为0表示报名费")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品图片")
    private String goodsImage;

    @Schema(description = "规格值列表")
    private List<ShopGoodsSpecValue> specValueList;

    @Schema(description = "单价")
    private BigDecimal saleAmount;

    @Schema(description = "购买数量")
    private Long quantity;

}
