package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区管理员
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_admin")
public class CommunityAdmin extends BaseEntity {

    private static final long serialVersionUID = -8125094301643784958L;

    @Schema(description = "管理员ID")
    @TableId(type = IdType.AUTO)
    private Long adminId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主")
    private Integer adminType;

    @Schema(description = "创建管理员ID")
    private Long createAdminId;

    @Schema(description = "创建管理员类型：0-默认 1-前台 2-后台")
    private Integer createAdminType;

}
