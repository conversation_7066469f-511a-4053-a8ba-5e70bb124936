package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/20
 */
@Data

public class RecommendCoachReq {
    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "current", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long current;

    @Schema(description = "size", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long size;
}
