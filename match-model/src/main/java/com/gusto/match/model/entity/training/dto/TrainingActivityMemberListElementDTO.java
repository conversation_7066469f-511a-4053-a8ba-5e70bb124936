package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练活动成员列表
 *
 * <AUTHOR>
 * @since 2021/9/15
 */
@Data

public class TrainingActivityMemberListElementDTO {
    @Schema(description = "成员用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "状态 0-默认 1-未签到 2-签到")
    private Integer state;

    @Schema(description = "性别 0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer sportType;

    @Schema(description = "教练等级 0-默认 1-准教练 2-初级教练 3-中级教练 4-高级教练")
    private Integer level;
}
