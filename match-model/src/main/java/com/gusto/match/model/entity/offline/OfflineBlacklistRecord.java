package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 线下赛黑名单记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_blacklist_record")
public class OfflineBlacklistRecord extends BaseEntity {

    private static final long serialVersionUID = 3895815961487292671L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "性别：0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "电话")
    private String mobile;

    @Schema(description = "证件号码")
    private String cardNumber;

    @Schema(description = "证件类型：0-默认 1-身份证 2-军官证 3-护照 4-港澳通行证 5-台胞证")
    private Integer cardType;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否启用")
    private Boolean enable;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

}
