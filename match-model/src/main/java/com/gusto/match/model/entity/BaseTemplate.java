package com.gusto.match.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.gusto.match.model.entity.match.TemplateField;
import com.gusto.match.model.mybatis.ListTemplateFieldJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 模板基类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseTemplate extends BaseEntity {
    public static final String TEMPLATE_ID_FIELD = "template_id";
    public static final String MATCH_ID_FIELD = "match_id";
    public static final String TITLE_FIELD = "title";
    public static final String OPEN_FIELD = "open";
    private static final long serialVersionUID = -5469794524082850440L;

    @Schema(description = "模板ID")
    @TableId(value = "template_id", type = IdType.AUTO)
    private Long templateId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "模板标题")
    private String title;

    @Schema(description = "是否开放")
    private Boolean open;

    @Schema(description = "证书背景")
    private String background;

    @Schema(description = "字段列表")
    @TableField(typeHandler = ListTemplateFieldJsonTypeHandler.class)
    private List<TemplateField> fieldList;
}
