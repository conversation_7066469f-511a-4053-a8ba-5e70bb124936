package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取用户陪跑记录详情响应
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
public class SunshineGetUserAccompanyRunDetailRsp {
    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "活动封面")
    private String coverImage;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点坐标")
    private String coordinate;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "发起人姓名")
    private String username;

    @Schema(description = "发起人头像")
    private String avatar;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "活动详情图片列表")
    private List<String> activityImageList;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "是否本人访问")
    private Boolean me;

    @Schema(description = "成员ID，me=false时返回")
    private Long memberId;

    @Schema(description = "陪跑距离，单位米，me=false时返回")
    private Double distance;

    @Schema(description = "是否限制陪跑距离，用于决定是否展示陪跑百分比")
    private Boolean distanceLimit;

    @Schema(description = "目标距离")
    private Double targetDistance;

    @Schema(description = "陪跑状态 0-默认 1-挑战者本人陪跑未结束 2-已报名陪跑并且完成 3-未报名 4-已报名未完成陪跑 5-未报名已结束 6-已报名 7-挑战者本人陪跑结束")
    private Integer state;

    @Schema(description = "默认名字")
    private String defaultUsername;

    @Schema(description = "项目名称")
    private String projectName;
}
