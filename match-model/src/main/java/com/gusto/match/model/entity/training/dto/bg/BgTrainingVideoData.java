package com.gusto.match.model.entity.training.dto.bg;

import com.gusto.match.model.entity.app.rsp.BgContentEventRsp;
import com.gusto.match.model.entity.training.rsp.TrainingVideoStaticRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 后台视频数据
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data

public class BgTrainingVideoData {

    @Schema(description = "视频数据")
    private TrainingVideoStaticRsp videoStatic;

    @Schema(description = "视频具体播放情况列表")
    private List<BgContentEventRsp> videoList;
}


