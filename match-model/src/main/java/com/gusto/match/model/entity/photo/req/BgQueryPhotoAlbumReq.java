package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-分页查询相册请求
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryPhotoAlbumReq {

    @Schema(description = "关联的线下赛赛事ID")
    private Long offlineRaceId;

    @Schema(description = "相册名称")
    private String albumName;

    @Schema(description = "相册状态：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;
}
