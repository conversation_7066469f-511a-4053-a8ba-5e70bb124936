package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * <p>
 * 发送消息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMessageSendReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long roomId;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 60)
    private String content;

}
