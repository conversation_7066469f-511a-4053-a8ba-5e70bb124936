package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 搜索陪跑记录
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
public class ChallengeUserAccompanyRunSearchReq {

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "current", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long current;

    @Schema(description = "size", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long size;

}
