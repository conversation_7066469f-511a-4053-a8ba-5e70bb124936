package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 推广数据
 *
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class TrainingPromoteData {
    @Schema(description = "教练分享海报次数")
    private Long coachSharePosterCount;

    @Schema(description = "教练分享个人主页次数")
    private Long coachShareProfileCount;

    @Schema(description = "H5教练主页访问次数")
    private Long coachH5ProfileCount;

    @Schema(description = "通过H5成为学员人数")
    private Long ByH5StudentCount;

    @Schema(description = "登录APP的学员人数")
    private Long loginStudentCount;
}
