package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.entity.offline.rsp.BatchImportOfflineUserSignBallotItem;
import com.gusto.match.model.mybatis.ListBatchImportOfflineUserSignBallotItemJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 线下赛用户报名中签导入任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_user_sign_import_ballot_job", autoResultMap = true)
public class OfflineUserSignImportBallotJob extends BaseTenantEntity {

    private static final long serialVersionUID = -7904675662034615475L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "导入总数")
    private Integer totalCount;

    @Schema(description = "成功更新的数量")
    private Integer successCount;

    @Schema(description = "导入失败的报名列表")
    @TableField(typeHandler = ListBatchImportOfflineUserSignBallotItemJsonTypeHandler.class)
    private List<BatchImportOfflineUserSignBallotItem> errorSignList;

}
