package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/26
 */
@Data

public class FullTrainingActivityDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;

    @Schema(description = "已报名人数")
    private Integer signCount;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "参与训练活动的头像")
    private List<String> memberAvatarList;

    @Schema(description = "用户签到有效距离，单位米")
    private Double signDistance;

    // 教练信息
    @Schema(description = "创建教练ID")
    private Long coachId;

    @Schema(description = "创建教练用户ID")
    private Long coachUserId;

    @Schema(description = "教练等级 0-默认 1-准教练 2-初级教练 3-中级教练 4-高级教练")
    private Integer level;

    @Schema(description = "教练名字")
    private String coachName;

    @Schema(description = "教练头像")
    private String coachAvatar;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "教练-省")
    private String province;

    @Schema(description = "教练-市")
    private String city;

    @Schema(description = "活动状态 0-默认 1-进行中 2-报名中 3-已结束")
    private Integer activityState;

    @Schema(description = "教练-跑龄，单位月")
    private Long runMonths;
}
