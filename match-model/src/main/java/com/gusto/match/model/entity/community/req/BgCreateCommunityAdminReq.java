package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.checkerframework.checker.index.qual.Positive;

import javax.validation.constraints.PositiveOrZero;
import java.util.Collections;
import java.util.List;

/**
 * 创建管理员
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class BgCreateCommunityAdminReq {

    @Schema(description = "用户ID列表，和手机号列表二选一")
    private List<Long> userIdList = Collections.emptyList();

    @Schema(description = "手机号列表，和用户ID列表二选一")
    private List<String> mobileList = Collections.emptyList();

    @Schema(description = "话题ID，不传表示超级管理员", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Long topicId;

    @Schema(description = "管理员类型：0-默认 1-官方 2-版主", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer adminType;

}
