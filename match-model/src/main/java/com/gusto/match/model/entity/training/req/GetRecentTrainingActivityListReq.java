package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取最近活动列表请求
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Data

public class GetRecentTrainingActivityListReq {
    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "current", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long current;

    @Schema(description = "size", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long size;
}
