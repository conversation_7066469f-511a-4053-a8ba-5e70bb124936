package com.gusto.match.model.entity.photo.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计算照片价格响应
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CalculatePhotoPriceRsp {

    @Schema(description = "相册ID")
    private Long albumId;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "单张原价（元）")
    private BigDecimal originalSinglePrice;

    @Schema(description = "原总价（元）")
    private BigDecimal originalTotalPrice;

    @Schema(description = "实际总价（元）")
    private BigDecimal actualTotalPrice;

    @Schema(description = "优惠金额（元）")
    private BigDecimal discountAmount;

    @Schema(description = "是否有折扣")
    private Boolean hasDiscount;

    @Schema(description = "折扣信息")
    private DiscountInfo discountInfo;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DiscountInfo {

        @Schema(description = "折扣配置ID")
        private Long discountId;

        @Schema(description = "折扣名称")
        private String discountName;

        @Schema(description = "折扣类型：1-按比例折扣 2-固定价格")
        private Integer discountType;

        @Schema(description = "折扣比例（百分比，如90表示9折）")
        private BigDecimal discountRatio;

        @Schema(description = "折扣后单价（元）")
        private BigDecimal discountPrice;
    }
}
