package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;

/**
 * 批量回复祝福
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class ChallengeRecordBatchReplyBlessingReq {

    @Schema(description = "赛季ID")
    @Positive
    private Long seasonId;

    @Schema(description = "记录ID")
    @PositiveOrZero
    private Long recordId;

    @Schema(description = "回复")
    @NotEmpty
    @Size(max = 200)
    private String reply;

}
