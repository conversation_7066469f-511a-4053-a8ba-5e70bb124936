package com.gusto.match.model.entity.community.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class CommunityLikeDTO {

    @Schema(description = "点赞ID")
    private Long likeId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论")
    private Integer objectType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "是否已读")
    @TableField("`read`")
    private Boolean read;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "创建时间")
    private Long createTime;

}
