package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.TrainingActivityListItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取训练活动的置顶列表响应，即显示在某个页面的前N个训练活动列表
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data

public class GetTrainingActivityTopListRsp {
    @Schema(description = "训练活动列表")
    private List<TrainingActivityListItemDTO> activityList;

    @Schema(description = "是否包含更多")
    private Boolean more;

    @Schema(description = "总数")
    private Long total;
}
