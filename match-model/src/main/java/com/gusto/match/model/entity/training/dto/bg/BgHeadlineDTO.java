package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/8
 */
@Data

public class BgHeadlineDTO {
    @Schema(description = "头条ID")
    private Long headlineId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "副标题")
    private String subtitle;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    private Long createTime;
}
