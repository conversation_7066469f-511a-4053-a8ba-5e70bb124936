package com.gusto.match.model.entity.location.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class BgLocationShareMemberDTO {

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "房间ID，为0表示离开房间")
    private Long roomId;

    @Schema(description = "原房间ID")
    private Long originRoomId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "是否房主")
    private Boolean owner;

    @Schema(description = "共享状态：0-默认 1-共享 2-不共享")
    private Integer shareState;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "最近更新位置时间")
    private Long lastUpdateLocationTime;

    @Schema(description = "房间开始时间")
    private Long roomStartTime;

    @Schema(description = "房间结束时间")
    private Long roomEndTime;

    @Schema(description = "最近更新房间时间")
    private Long lastUpdateRoomTime;

    @Schema(description = "运动时间，单位秒")
    private Long duration;

    @Schema(description = "运动距离，单位米")
    private Double distance;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "实时配速，单位秒")
    private Double currentPace;

    @Schema(description = "平均步频，单位步/分钟")
    private Integer averageStepRate;

    @Schema(description = "平均步幅，单位厘米")
    private Double averageStride;

    @Schema(description = "累计上升，单位米")
    private Double totalAscent;

    @Schema(description = "方向")
    private Double direction;

    @Schema(description = "是否运动中")
    private Boolean moving;

    @Schema(description = "是否展示方向")
    private Boolean showDirection;

}
