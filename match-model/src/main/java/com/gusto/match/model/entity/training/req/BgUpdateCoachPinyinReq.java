package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/11/24
 */
@Data

public class BgUpdateCoachPinyinReq {
    @Schema(description = "教练ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long coachId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "新的拼音", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pinyin;
}
