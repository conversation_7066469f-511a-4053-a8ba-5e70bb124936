package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * 更新感谢信
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ChallengeRecordUpdateThankLetterReq {

    @Schema(description = "陪跑ID")
    @Positive
    private Long recordId;

    @Schema(description = "感谢信")
    @NotEmpty
    @Size(max = 200)
    private String thankLetter;

    @Schema(description = "签名")
    private String signature;

}
