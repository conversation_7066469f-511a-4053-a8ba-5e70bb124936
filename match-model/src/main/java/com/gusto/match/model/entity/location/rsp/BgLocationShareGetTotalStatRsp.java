package com.gusto.match.model.entity.location.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取总统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class BgLocationShareGetTotalStatRsp {

    @Schema(description = "总房间数")
    private Integer totalRoomCount;

    @Schema(description = "总成员数")
    private Integer totalMemberCount;

    @Schema(description = "今天房间数")
    private Integer todayRoomCount;

    @Schema(description = "今天成员数")
    private Integer todayMemberCount;

    @Schema(description = "昨天房间数")
    private Integer yesterdayRoomCount;

    @Schema(description = "昨天成员数")
    private Integer yesterdayMemberCount;

    @Schema(description = "本月房间数")
    private Integer currentMonthRoomCount;

    @Schema(description = "本月成员数")
    private Integer currentMonthMemberCount;

    @Schema(description = "上月房间数")
    private Integer lastMonthRoomCount;

    @Schema(description = "上月成员数")
    private Integer lastMonthMemberCount;

}
