package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛文档
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_document")
public class OfflineDocument extends BaseTenantEntity {

    private static final long serialVersionUID = 8104144234877314592L;

    @Schema(description = "文档ID")
    @TableId(type = IdType.AUTO)
    private Long documentId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "文档类型：0-默认 1-赛事介绍 2-竞赛规程 3-报名须知 4-参赛风险 5-参赛声明 6-时间安排表 7-比赛路线图 8-其他 9-外部链接 10-衣服尺码 11-套餐说明")
    private Integer documentType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "LOGO")
    private String logo;

    @Schema(description = "是否启用")
    private Boolean enable;

    @Schema(description = "文档内容")
    private String content;

    @Schema(description = "排序，降序")
    private Integer sort;

    @Schema(description = "语言类型：0-默认 1-中文 2-英文")
    private Integer languageType = 1;

}
