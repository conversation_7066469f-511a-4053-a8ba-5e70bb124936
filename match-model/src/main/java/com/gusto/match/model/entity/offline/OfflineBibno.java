package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛号码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_bibno")
public class OfflineBibno extends BaseTenantEntity {

    private static final long serialVersionUID = -7344354687823747669L;

    @Schema(description = "号码ID")
    @TableId(type = IdType.AUTO)
    private Long bibnoId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "规则ID")
    private Long ruleId;

    @Schema(description = "前缀")
    private String sub;

    @Schema(description = "长度")
    private Integer length;

    @Schema(description = "号码")
    private Integer number;

    @Schema(description = "限制性别：0-默认 1-男 2-女")
    private Integer sex;

    @Schema(description = "通道ID")
    private Long channelId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户ID")
    private Long signId;

    @Schema(description = "使用状态：0-默认 1-未使用 2-已使用")
    private Integer useState;

    @Schema(description = "生成类型：0-默认 1-规则 2-预留 3-导入")
    private Integer genType;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "星级")
    private Integer star;

    /**
     * 去除前缀的完整号码
     */
    public String pureNumber() {
        return fullBibno.replace(sub, "");
    }

    public void release() {
        userId = 0L;
        signId = 0L;
        useState = 1;
    }

}
