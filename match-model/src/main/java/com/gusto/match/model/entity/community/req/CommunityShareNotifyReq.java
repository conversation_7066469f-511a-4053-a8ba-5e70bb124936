package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
public class CommunityShareNotifyReq {

    @Schema(description = "话题ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long topicId;

    @Schema(description = "动态ID，为0表示分享话题，不为0表示分享动态", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private Long postId;

}
