package com.gusto.match.model.entity.community.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
public class CommunityAdminDTO {

    @Schema(description = "管理员ID")
    private Long adminId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "创建时间")
    private Long createTime;

}
