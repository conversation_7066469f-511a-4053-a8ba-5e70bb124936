package com.gusto.match.model.entity.challenge.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.match.model.entity.challenge.dto.ProjectDTO;
import com.gusto.match.model.entity.user.dto.UserShippingAddressDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛报名详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class GetChallengeSignDetailRsp {

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目列表")
    private List<ProjectDTO> projectList;

    @Schema(description = "报名卡ID，为0表示没有报名卡，不为0表示本人或第一张报名卡")
    private Long userCardId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "证件号")
    private String cardNumber;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "收货地址，没有添加地址时不返回")
    private UserShippingAddressDTO shippingAddress;
}
