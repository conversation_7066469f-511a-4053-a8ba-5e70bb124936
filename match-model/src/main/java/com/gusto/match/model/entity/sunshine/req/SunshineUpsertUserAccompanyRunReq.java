package com.gusto.match.model.entity.sunshine.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 发起陪跑请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineUpsertUserAccompanyRunReq {

    @Schema(description = "记录ID，更新时必填")
    @Positive
    private Long recordId;

    @Schema(description = "赛季ID，创建时必填")
    @Positive
    private Long seasonId;

    @Schema(description = "活动封面", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String coverImage;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 50)
    private String activityName;

    @Schema(description = "活动地点坐标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String coordinate;

    @Schema(description = "活动地点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String location;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long startTime;

    @Schema(description = "活动详情", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 2000)
    private String activityDetail;

    @Schema(description = "活动详情图片列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 3)
    private List<String> activityImageList;
}
