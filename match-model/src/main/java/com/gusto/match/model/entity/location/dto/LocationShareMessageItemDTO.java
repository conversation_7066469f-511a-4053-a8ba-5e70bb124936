package com.gusto.match.model.entity.location.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMessageItemDTO {

    @Schema(description = "消息ID")
    private Long messageId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "消息类型：0-默认 1-普通 2-进入房间 3-离开房间 4-开始共享 5-结束共享")
    private Integer messageType;

    @Schema(description = "创建时间")
    private Long createTime;

}
