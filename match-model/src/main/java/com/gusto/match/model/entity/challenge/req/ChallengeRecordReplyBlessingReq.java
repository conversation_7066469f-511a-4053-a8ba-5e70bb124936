package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

/**
 * 回复祝福
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class ChallengeRecordReplyBlessingReq {

    @Schema(description = "祝福ID")
    @Positive
    private Long blessId;

    @Schema(description = "回复")
    @NotEmpty
    @Size(max = 200)
    private String reply;

}
