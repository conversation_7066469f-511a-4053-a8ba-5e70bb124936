package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 项目绑定套餐
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class BgProjectPackageDTO {

    @Schema(description = "套餐名称")
    private String name;

    @Schema(description = "排序，数值越大越靠前")
    private Integer sort;

    @Schema(description = "商品ID列表")
    private List<Long> goodsIds;

}
