package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.mybatis.ListSunshineProjectPackageJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 赛事项目
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sunshine_project", autoResultMap = true)
public class SunshineProject extends BaseEntity {

    private static final long serialVersionUID = -5334038274858140525L;

    @Schema(description = "项目ID")
    @TableId(value = "project_id", type = IdType.AUTO)
    private Long projectId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛事赛季ID")
    private Long seasonId;

    @Schema(description = "项目名称")
    private String title;

    @Schema(description = "支付金额：0为免费，不用支付")
    private BigDecimal price;

    @Schema(description = "项目状态：0-未上线 1-上线")
    private Integer state;

    @Schema(description = "号码数字长度")
    private Integer bibnoLength;

    @Schema(description = "号码前缀")
    private String bibnoSub;

    @Schema(description = "号码初始值")
    private Long bibnoInitNumber;

    @Schema(description = "号码自增最大值")
    private Integer bibnoIncreaseMax;

    // enum BibnoIncreaseRule
    @Schema(description = "自增规则：0-按增幅增加 1-随机")
    private Integer bibnoIncreaseRule;

    @Schema(description = "号码布模板ID")
    private Long bibnoTemplateId;

    @Schema(description = "证书模板ID")
    private Long certTemplateId;

    @Schema(description = "发起陪跑证书模板ID")
    private Long ownTemplateId;

    @Schema(description = "项目绑定套餐列表")
    @TableField(typeHandler = ListSunshineProjectPackageJsonTypeHandler.class)
    private List<SunshineProjectPackage> projectPackageList;

    @Schema(description = "是否开放陪跑")
    private Boolean withRunning;

    @Schema(description = "参与陪跑证书模板ID")
    private Long accompanyTemplateId;

    @Schema(description = "项目类型：0-个人赛 1-助力赛 2-个人限时赛")
    private Integer projectType;

    @Schema(description = "挑战目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "目标类型：0-固定 1-定长减年龄 2-年龄")
    private Integer targetType;

    @Schema(description = "（助力）队伍人数限制：0为不限制")
    private Integer memberLimit = 0;

    @Schema(description = "（助力）我的助力人数限制：0为不限制")
    private Integer helpLimit;

    @Schema(description = "（助力）助力时间限制，单位小时")
    private Integer hourLimit;

    @Schema(description = "（个人限时）单次最短距离，单位米，0为不限制")
    private Double minDistance;

    // enum ProjectTimeLimitType
    @Schema(description = "（个人限时）限时类型：1-每天 2-每周 3-每月")
    private Integer timeLimitType;

    @Schema(description = "挑战目标描述")
    private String targetDesc;

    @Schema(description = "证书背景图")
    private String certBackgroundImage;

    @Schema(description = "发起陪跑证书模板背景图")
    private String ownBackgroundImage;

    @Schema(description = "参与陪跑证书模板背景图")
    private String accompanyBackgroundImage;

    @Schema(description = "号码布背景")
    private String bibnoBackgroundImage;

    @Schema(description = "号码前缀颜色")
    private String bibnoSubColor;

    @Schema(description = "号码颜色")
    private String bibnoColor;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "证书印章图片")
    private String certStampImage;
}
