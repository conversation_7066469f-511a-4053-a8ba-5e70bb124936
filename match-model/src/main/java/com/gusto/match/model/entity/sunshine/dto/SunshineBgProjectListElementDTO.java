package com.gusto.match.model.entity.sunshine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-赛事项目列表元素
 */
@Data
public class SunshineBgProjectListElementDTO {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String title;

    @Schema(description = "项目状态：0-未上线 1-上线")
    private Integer state;

    @Schema(description = "是否开放陪跑")
    private Boolean withRunning;

    @Schema(description = "项目类型：0-个人赛 1-助力赛 2-个人限时赛")
    private Integer projectType;

    @Schema(description = "挑战目标距离，单位米")
    private Double targetDistance;

}
