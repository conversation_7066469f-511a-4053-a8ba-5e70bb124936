package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-用户陪跑成员
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data

public class BgUserAccompanyRunMemberDTO {

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "排序序号，默认为0，越大越靠前")
    private Integer sort = 0;

    @Schema(description = "报名时间")
    private Long signTime;

    @Schema(description = "实际贡献距离，单位米")
    private Double distance;

    @Schema(description = "成员用户ID")
    private Long userId;

    @Schema(description = "总跑团ID")
    private Long headClubId;

    @Schema(description = "跑团名称")
    private String clubName;

    @Schema(description = "陪跑方式 0-默认 1-到场 2-异地")
    private Integer accompanyMode;

}
