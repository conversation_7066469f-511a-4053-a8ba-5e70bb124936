package com.gusto.match.model.entity.training;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/11/24
 */
@Data

public class CoachLevelCertMessage implements Serializable {
    private static final long serialVersionUID = 5175208738336750714L;

    @Schema(description = "等级名字(中文)")
    private String chineseLevelName;

    @Schema(description = "等级名字(英文)")
    private String englishLevelName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "升级时间")
    private String levelUpTime;

    @Schema(description = "升级年份")
    private String year;

    @Schema(description = "教练等级")
    private String coachLevel;
}
