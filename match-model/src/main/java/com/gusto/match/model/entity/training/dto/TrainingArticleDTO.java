package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练文章
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data

public class TrainingArticleDTO {
    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "封面")
    private String coverImg;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章链接")
    private String url;

    @Schema(description = "发布时间")
    private Long postTime;
}
