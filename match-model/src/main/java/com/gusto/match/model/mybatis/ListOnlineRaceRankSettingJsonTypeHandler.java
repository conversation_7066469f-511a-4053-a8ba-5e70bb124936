package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.online.OnlineRaceRankSetting;

import java.io.IOException;
import java.util.List;

/**
 * eg: <result column="size_list" property="sizeList" jdbcType="VARCHAR" typeHandler="com.gusto.match.model.mybatis
 * .ListStringJsonTypeHandler"/>
 * <p>
 * ps: 记得 @TableName(autoResultMap = true)
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public class ListOnlineRaceRankSettingJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<OnlineRaceRankSetting>> typeReference = new TypeReference<>() {
    };

    public ListOnlineRaceRankSettingJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
