package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练知识（视频和文章）后台状态类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data

public class BgTrainingKnowledgeStatusReq {

    @Schema(description = "编辑视频时为视频ID，编辑文章时为文章ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long knowledgeId;

    @Schema(description = "知识类型，0=视频，1=知识文章，2=装备评测", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "是否置顶，false=不置顶，true=置顶", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean top;

    @Schema(description = "是否可见，false=隐藏，true=可见", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean visible;

    @Schema(description = "是否删除，false=不删除，true=删除", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean deleted;
}
