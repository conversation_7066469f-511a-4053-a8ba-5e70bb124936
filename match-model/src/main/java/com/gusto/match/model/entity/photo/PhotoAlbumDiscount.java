package com.gusto.match.model.entity.photo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 相册折扣配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "photo_album_discount")
public class PhotoAlbumDiscount extends BaseTenantEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "折扣配置ID")
    @TableId(type = IdType.AUTO)
    private Long discountId;

    @Schema(description = "关联的相册ID")
    private Long albumId;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "折扣比例（百分比，如90表示9折）")
    private BigDecimal discountRatio;

    @Schema(description = "折扣后单价（元）")
    private BigDecimal discountPrice;

    @Schema(description = "折扣类型：1-按比例折扣 2-固定价格")
    private Integer discountType;

    @Schema(description = "折扣名称（如：3张9折、5张8折）")
    private String discountName;

    @Schema(description = "是否启用：0-禁用 1-启用")
    private Integer status;

    @Schema(description = "排序权重，数值越小排序越靠前")
    private Integer sortOrder;

    @Schema(description = "备注信息")
    private String remark;

    /**
     * 检查折扣配置是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为按比例折扣
     */
    public boolean isRatioDiscount() {
        return discountType != null && discountType == 1;
    }

    /**
     * 检查是否为固定价格折扣
     */
    public boolean isFixedPriceDiscount() {
        return discountType != null && discountType == 2;
    }

    /**
     * 计算折扣后的总价
     */
    public BigDecimal calculateTotalPrice(BigDecimal originalSinglePrice) {
        if (quantity == null || quantity <= 0) {
            return BigDecimal.ZERO;
        }

        if (isFixedPriceDiscount() && discountPrice != null) {
            // 固定价格折扣
            return discountPrice.multiply(BigDecimal.valueOf(quantity));
        } else if (isRatioDiscount() && discountRatio != null && originalSinglePrice != null) {
            // 按比例折扣
            BigDecimal totalOriginalPrice = originalSinglePrice.multiply(BigDecimal.valueOf(quantity));
            return totalOriginalPrice.multiply(discountRatio).divide(BigDecimal.valueOf(100));
        }

        return BigDecimal.ZERO;
    }
}
