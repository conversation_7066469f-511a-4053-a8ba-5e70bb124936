package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

/**
 * <p>
 * 社区动态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "community_post", autoResultMap = true)
public class CommunityPost extends BaseEntity {

    private static final long serialVersionUID = -6382325445782466643L;

    @Schema(description = "动态ID")
    @TableId(type = IdType.AUTO)
    private Long postId;

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "作者类型：0-默认 1-用户 2-官方")
    private Integer authorType;

    @Schema(description = "作者ID")
    private Long authorId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "标题，限制20字")
    private String title;

    @Schema(description = "内容，限制1000字")
    private String content;

    @Schema(description = "图片列表，限制9张")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> imageList;

    @Schema(description = "封面图高度")
    private Double imageHeight;

    @Schema(description = "封面图宽度")
    private Double imageWidth;

    @Schema(description = "链接")
    private String url;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "分享量")
    private Integer shareCount;

    @Schema(description = "关联的跑步记录ID")
    private Long runRecordId;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer activityType;

    @Schema(description = "总距离，单位米")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    private Integer totalDuration;

    @Schema(description = "设备")
    private String deviceModel;

    @Schema(description = "运动天数")
    private Integer runDayCount;

    @Schema(description = "IP")
    private String ip;

    @Schema(description = "IP省")
    private String ipProvince;

    @Schema(description = "IP市")
    private String ipCity;

    @Schema(description = "发布时间")
    private Instant postTime;

    @Schema(description = "最后修改时间")
    private Instant lastUpdateTime;

    @Schema(description = "最后评论时间")
    private Instant lastReplyTime;

    @Schema(description = "是否允许评论")
    private Boolean allowComment;

    @Schema(description = "是否展示评论")
    private Boolean showComment;

    @Schema(description = "状态：0-默认 1-展示 2-用户删除 3-管理员删除 4-限流 5-审核不通过")
    private Integer state;

    @Schema(description = "乐观锁版本")
    @Version
    private Integer version;

    @Schema(description = "图片审核请求ID")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> imageReviewRequestId;

    @Schema(description = "图片审核结果：0-默认 1-未检测到风险 2-低风险 3-中风险 4-高风险 5-检测失败")
    private Integer imageReviewResult;

    @Schema(description = "文本审核请求ID")
    private String textReviewRequestId;

    @Schema(description = "文本审核结果：0-默认 1-未检测到风险 2-低风险 3-中风险 4-高风险 5-检测失败")
    private Integer textReviewResult;

}
