package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import com.gusto.match.model.mybatis.ListLongJsonTypeHandler;
import com.gusto.match.model.mybatis.ListStringJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

/**
 * 用户陪跑记录
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "match_user_accompany_run", autoResultMap = true)
public class UserAccompanyRun extends BaseEntity {

    private static final long serialVersionUID = -3380233137068108852L;

    @Schema(description = "记录ID")
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点坐标")
    private String coordinate;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Instant startTime;

    @Schema(description = "结束时间")
    private Instant endTime;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "活动详情图片列表")
    @TableField(typeHandler = ListStringJsonTypeHandler.class)
    private List<String> activityImageList;

    @Deprecated(since = "2025-03-21")
    @Schema(description = "证书展示成员ID列表")
    @TableField(exist = false, typeHandler = ListLongJsonTypeHandler.class)
    private List<Long> showMemberIds;

    @Schema(description = "是否对外展示目标跑量")
    private Boolean showTargetDistance;

    @Schema(description = "感谢信")
    private String thankLetter;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "是否启用感谢信，兼容旧陪跑，旧陪跑不参与感谢信逻辑")
    private Boolean enableThankLetter;

    @Schema(description = "总跑团ID")
    private Long headClubId;

}
