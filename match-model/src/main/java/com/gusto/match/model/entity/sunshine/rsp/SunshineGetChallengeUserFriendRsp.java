package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 获取挑战赛已报名好友响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeUserFriendRsp {

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "报名时间")
    private Long signTime;

    @Schema(description = "挑战状态 0-未挑战 1-挑战成功 2-挑战失败")
    private Integer finishedState;

}
