package com.gusto.match.model.entity.training.dto.simple;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 训练活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Data

public class SimpleTrainingActivityDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "训练营名称")
    private String campName;

    @Schema(description = "训练活动名称")
    private String name;

    @Schema(description = "是否与用户相关")
    private Boolean related;
}
