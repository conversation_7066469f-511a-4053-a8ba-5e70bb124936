package com.gusto.match.model.entity.offline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 模板字段
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
public class OfflineTemplateField implements Serializable {

    private static final long serialVersionUID = 564969605322588218L;

    @Schema(
            description = "字段序号：0-默认 " +
                    "1-姓名 2-项目 3-参赛号 4-尺寸 5-性别" +
                    "6-照片 7-枪声成绩 8-净成绩 9-终点成绩 10-项目名次(净) 11-性别名次(净) 12-年龄名次(净) 13-项目名次(枪) 14-性别名次(枪) " +
                    "15-年龄名次(枪) 16-项目人数 17-性别人数 18-年龄人数 19-平均配速 20-平均时速 21-累计爬升" +
                    "22-总用时1 23-总用时2 24-总用时3 25-总用时4 26-总用时5 27-总用时6 28-总用时7 29-总用时8 30-总用时9 31-总用时10" +
                    "32-总用时11 33-总用时12 34-总用时13 35-总用时14 36-总用时15" +
                    "37-区间用时1 38-区间用时2 39-区间用时3 40-区间用时4 41-区间用时5 42-区间用时6 43-区间用时7 44-区间用时8 45-区间用时9 46-区间用时10" +
                    "47-区间用时11 48-区间用时12 49-区间用时13 50-区间用时14 51-区间用时15"
    )
    private Integer fieldNumber;

    @Schema(description = "字段类型：0-默认 1-文本 2-图片")
    private Integer fieldType;

    @Schema(description = "字体大小")
    private Integer fontSize;

    @Schema(description = "字体颜色")
    private String fontColor;

    @Schema(description = "x轴")
    private Integer x;

    @Schema(description = "y轴")
    private Integer y;

    @Schema(description = "字体对齐方式：0-居左 1-居中 2-居右")
    private Integer alignment;

    @Schema(description = "CSS")
    private String style;

    @Schema(description = "内容")
    private String content;

}
