package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.challenge.dto.ChallengeRecordBlessingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 获取用户陪跑记录详情响应
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
public class GetUserAccompanyRunDetailRsp {

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "赛事ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点坐标")
    private String coordinate;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "发起人姓名")
    private String username;

    @Schema(description = "发起人头像")
    private String avatar;

    @Schema(description = "活动详情")
    private String activityDetail;

    @Schema(description = "活动详情图片列表")
    private List<String> activityImageList;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际挑战距离，单位米")
    private Double actualDistance;

    @Schema(description = "头图-挑战成功")
    private String headImageForSuccess;

    @Schema(description = "头图-挑战失败")
    private String headImageForFailure;

    @Schema(description = "感谢信")
    private String thankLetter;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "是否本人访问")
    private Boolean me;

    @Schema(description = "是否报名")
    private Boolean sign;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑距离，单位米")
    private Double memberDistance;

    @Schema(description = "陪跑方式 0-默认 1-到场 2-异地")
    private Integer accompanyMode;

    @Schema(description = "我的祝福列表")
    private List<ChallengeRecordBlessingDTO> blessingList;

    @Schema(description = "默认名字")
    private String defaultUsername;

    @Schema(description = "是否陪跑过该发起者")
    private Boolean signed;

    @Schema(description = "是否对外展示目标跑量")
    private Boolean showTargetDistance;

    @Schema(description = "成员名字")
    private String memberUsername;

    @Schema(description = "挑战失败的鼓励")
    private String encourage;

    @Schema(description = "最新陪跑记录ID")
    private Long latestRecordId;

    @Schema(description = "是否启用感谢信，兼容旧陪跑，旧陪跑不参与感谢信逻辑")
    private Boolean enableThankLetter;

    @Schema(description = "总跑团ID")
    private Long headClubId;

    @Schema(description = "跑团名称")
    private String clubName;

    @Schema(description = "成员总跑团ID")
    private Long memberClubId;

    @Schema(description = "成员跑团名称")
    private String memberClubName;

}
