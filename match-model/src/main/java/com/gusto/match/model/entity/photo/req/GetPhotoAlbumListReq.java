package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取相册列表请求
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPhotoAlbumListReq {

    @Schema(description = "关联的线下赛赛事ID", required = true)
    @NotNull(message = "线下赛ID不能为空")
    private Long offlineRaceId;
}
