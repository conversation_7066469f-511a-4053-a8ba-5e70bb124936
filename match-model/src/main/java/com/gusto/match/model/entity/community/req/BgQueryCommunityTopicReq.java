package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取社区话题列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgQueryCommunityTopicReq {

    @Schema(description = "名称")
    private String name = "";

    @Schema(description = "创建月份")
    private Long createTime = 0L;

    @Schema(description = "分类：0-默认 101-线下赛 102-线上赛 201-马拉松 202-越野 203-铁人三项 204-徒步")
    private Integer category = 0;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
