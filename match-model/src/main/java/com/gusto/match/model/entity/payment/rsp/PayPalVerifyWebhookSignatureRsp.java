package com.gusto.match.model.entity.payment.rsp;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
public class PayPalVerifyWebhookSignatureRsp {

    @Schema(description = "SUCCESS or FAILURE")
    @JSONField(name = "verification_status", ordinal = 1)
    private String verificationStatus;

}
