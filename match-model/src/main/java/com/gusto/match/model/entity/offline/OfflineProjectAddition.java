package com.gusto.match.model.entity.offline;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 线下赛项目附加信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
public class OfflineProjectAddition implements Serializable {

    private static final long serialVersionUID = -2088179211741243937L;

    @Schema(description = "附加信息类型：0-默认 1-图片 2-文字 3-选项 4-用时 5-视频 6-历史成绩")
    private Integer additionType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "上传类型：0-默认 1-必填 2-非必填 3-往届有成绩可不填 4-往届有成绩/APP有记录可不填")
    private Integer uploadType;

    @Schema(description = "选项列表，用`#`分割")
    private String optionList;

    @Schema(description = "是否需要审核")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean needReview = true;

}
