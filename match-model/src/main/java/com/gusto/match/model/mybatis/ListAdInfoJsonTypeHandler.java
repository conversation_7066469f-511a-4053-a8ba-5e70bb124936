package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.habit.AdInfo;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/15
 */
public class ListAdInfoJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<AdInfo>> typeReference = new TypeReference<>() {
    };

    public ListAdInfoJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
