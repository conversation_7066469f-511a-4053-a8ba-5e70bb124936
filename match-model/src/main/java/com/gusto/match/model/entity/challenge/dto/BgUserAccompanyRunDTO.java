package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-用户陪跑记录
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data

public class BgUserAccompanyRunDTO {

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "陪跑团贡献距离")
    private Double totalDistance;

    @Schema(description = "总跑团ID")
    private Long headClubId;

    @Schema(description = "跑团名称")
    private String clubName;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "性别：1-男 2-女")
    private Integer sex;

    @Schema(description = "电话")
    private String mobile;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "完赛状态：0-默认 1-未挑战 2-挑战成功 3-挑战失败")
    private Integer finishedState;

}
