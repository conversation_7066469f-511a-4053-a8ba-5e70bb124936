package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;

/**
 * 更新陪跑报名
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class ChallengeRecordUpdateJoinReq {

    @Schema(description = "陪跑记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long recordId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String username;

    @Schema(description = "陪跑方式：0-默认 1-到场陪跑 2-异地陪跑", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer accompanyMode;

    @Schema(description = "总跑团ID")
    private Long headClubId;

}
