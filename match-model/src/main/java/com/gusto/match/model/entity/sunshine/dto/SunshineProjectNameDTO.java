package com.gusto.match.model.entity.sunshine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 挑战赛项目名称
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineProjectNameDTO {
    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "挑战目标")
    private String targetDesc;

    @Schema(description = "Logo")
    private String logo;
}
