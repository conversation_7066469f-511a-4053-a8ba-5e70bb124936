package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取房间列表
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Data
public class BgLocationShareMemberQueryReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long roomId = 0L;

    @Schema(description = "昵称")
    private String nickname = "";

    @Schema(description = "手机")
    private String mobile = "";

    @Schema(description = "共享状态：0-默认 1-共享 2-不共享")
    private Integer shareState = 0;

    @Schema(description = "用户ID")
    private Long userId = 0L;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
