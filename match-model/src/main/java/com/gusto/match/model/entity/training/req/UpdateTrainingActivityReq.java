package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/24
 */
@Data

public class UpdateTrainingActivityReq {
    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long activityId;

    @Schema(description = "活动封面", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coverImage;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer locationType;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double latitude;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double longitude;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String address;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "人数限制：0为不限制", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer numberLimit = 0;

    @Schema(description = "活动详情", requiredMode = Schema.RequiredMode.REQUIRED)
    private String activityDetail;

    @Schema(description = "是否同意免责协议", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean agree;
}
