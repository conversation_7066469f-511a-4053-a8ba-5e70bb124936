package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 陪跑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
public class ChallengeRecordMemberDTO {

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "报名时间")
    private Long signTime;

    @Schema(description = "实际贡献距离，单位米")
    private Double distance;

    @Schema(description = "陪跑方式 0-默认 1-到场 2-异地")
    private Integer accompanyMode;

}
