package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/26
 */
@Data
public class AccompanyMemberDTO {
    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "报名时间")
    private Long signTime;

    @Schema(description = "用户名字")
    private String username;

    @Schema(description = "陪跑方式 0-默认 1-到场 2-异地")
    private Integer accompanyMode;

    @Schema(description = "距离")
    private Double distance;
}
