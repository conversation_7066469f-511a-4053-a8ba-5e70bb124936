package com.gusto.match.model.entity.sunshine.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户参与的陪跑记录请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetUserJoinAccompanyRunRsp {

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "成员ID")
    private Long memberId;

    @Schema(description = "创建者名称")
    private String creatorName;

    @Schema(description = "创建者头像")
    private String creatorAvatar;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "陪跑距离")
    private Double distance;

    @Schema(description = "到场时间")
    private Long presentTime;
}
