package com.gusto.match.model.entity.training;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <p>
 * 教练
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "coach", autoResultMap = true)
public class Coach extends BaseEntity {
    private static final long serialVersionUID = -2607509494885563527L;

    @TableId(value = "coach_id", type = IdType.AUTO)
    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "是否进行过教程演示")
    private Boolean showTutorial;

    @Schema(description = "教练编号")
    private String coachNumber;

    @Schema(description = "参与的万名教练计划")
    private Long planId;

    @Schema(description = "开始跑步时间")
    private Instant runStartTime;

    // v1.2
    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "教练等级")
    private Integer level;

    @Schema(description = "是否锁定")
    private Boolean locked;

    @Schema(description = "排序：越大越前面")
    private Integer sort;

    // v1.3
    @Schema(description = "等级提升时间")
    private Instant levelUpTime;

    @Schema(description = "等级是否有提升")
    private Boolean levelUpChange;

    @Schema(description = "名字拼音")
    private String pinyinName;

    // v3.2.0
    @Schema(description = "客户端访问次数")
    private Long appViewCount;

    @Schema(description = "h5访问次数")
    private Long h5ViewCount;

    @Schema(description = "聊天次数")
    private Long chatTimes;
}
