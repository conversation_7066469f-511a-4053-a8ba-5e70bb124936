package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区动态标签关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_post_tag_relation")
public class CommunityPostTagRelation extends BaseEntity {

    private static final long serialVersionUID = 9052071972165239573L;

    @Schema(description = "关联ID")
    @TableId(type = IdType.AUTO)
    private Long relationId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "标签ID")
    private Long tagId;

}
