package com.gusto.match.model.entity.photo;

import lombok.Data;

/**
 * PhotoPlus API 通用响应结构
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
public class PhotoPlusCommonRsp<T> {

    /**
     * 状态码 1:成功 -1:失败
     */
    private Integer code;

    /**
     * 提示消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应数据
     */
    private T result;

    /**
     * 元数据
     */
    private Object meta;

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 1;
    }

}
