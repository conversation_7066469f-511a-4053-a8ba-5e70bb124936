package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 挑战赛项目
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class ProjectDTO {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "挑战目标描述")
    private String targetDesc;

    @Schema(description = "项目绑定套餐列表")
    private List<ProjectPackageDTO> projectPackageList;

    @Schema(description = "报名费")
    private BigDecimal signAmount;
}
