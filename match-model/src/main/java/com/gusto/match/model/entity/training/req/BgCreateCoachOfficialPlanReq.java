package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/11
 */
@Data

public class BgCreateCoachOfficialPlanReq {
    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "期号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long number;

    @Schema(description = "课程开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long classStartTime;

    @Schema(description = "课程结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long classEndTime;
}
