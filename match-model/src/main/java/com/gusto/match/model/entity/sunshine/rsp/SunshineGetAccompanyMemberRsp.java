package com.gusto.match.model.entity.sunshine.rsp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gusto.match.model.entity.sunshine.dto.SunshineAccompanyMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/26
 */
@Data
public class SunshineGetAccompanyMemberRsp {
    @Schema(description = "陪跑名单")
    private IPage<SunshineAccompanyMemberDTO> accompanyMember;

    @Schema(description = "报名总人数")
    private Integer signCount;

    @Schema(description = "异地人数")
    private Integer differentPlaceCount;

    @Schema(description = "到场人数")
    private Integer presentCount;

    @Schema(description = "状态 0-默认 1-进行中 2-结束")
    private Integer state;
}
