package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/12/17
 */
@Data

public class WhiteRunnerRsp {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "教练ID")
    private Long coachId;

    @Schema(description = "小白用户数量")
    private Integer whiteRun;

    @Schema(description = "教练真实姓名")
    private String realName;

    @Schema(description = "教练等级")
    private Integer level;

    @Schema(description = "是否锁定")
    private Boolean locked;

}
