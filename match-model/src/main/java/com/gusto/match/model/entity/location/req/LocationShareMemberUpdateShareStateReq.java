package com.gusto.match.model.entity.location.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 更新共享状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
public class LocationShareMemberUpdateShareStateReq {

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long roomId;

    @Schema(description = "共享状态：0-默认 1-共享 2-不共享", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer shareState;

}
