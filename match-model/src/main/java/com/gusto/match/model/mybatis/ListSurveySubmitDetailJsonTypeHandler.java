package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.survey.SurveySubmitDetail;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-05
 */
public class ListSurveySubmitDetailJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<SurveySubmitDetail>> typeReference = new TypeReference<>() {
    };

    public ListSurveySubmitDetailJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
