package com.gusto.match.model.entity.sunshine.rsp;

import com.gusto.match.model.entity.sunshine.dto.SunshineProjectNameDTO;
import com.gusto.match.model.entity.sunshine.dto.SunshineSeasonNameDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 获取挑战赛详情响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class SunshineGetChallengeDetailRsp {
    @Schema(description = "挑战赛 ID")
    private Long matchId;

    @Schema(description = "赛季 ID")
    private Long seasonId;

    @Schema(description = "挑战赛名称")
    private String matchName;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "报名人数")
    private Integer signCount;

    @Schema(description = "报名开始时间")
    private Long signStartTime;

    @Schema(description = "报名截止时间")
    private Long signEndTime;

    @Schema(description = "赛事开始时间")
    private Long startTime;

    @Schema(description = "赛事截止时间")
    private Long endTime;

    @Schema(description = "封面图")
    private String coverImage;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "详情图")
    private String detailImage;

    @Schema(description = "Q&A")
    private String question;

    @Schema(description = "赛季名称列表")
    private List<SunshineSeasonNameDTO> seasonNameList;

    @Schema(description = "已报名好友头像列表，已登录时返回")
    private List<String> signFriendAvatarList;

    @Schema(description = "已报名好友人数，已登录时返回")
    private Integer signFriendCount;

    @Schema(description = "是否展示陪跑，已登录时返回")
    private Boolean showAccompany;

    @Schema(description = "参与陪跑人数，已登录时返回")
    private Integer accompanyMemberCount;

    @Schema(description = "项目名称列表")
    private List<SunshineProjectNameDTO> projectNameList;

    @Schema(description = "报名状态 0-默认 1-暂未开始报名 2-我也要报名 3-活动已结束(未报名) 4-已截止报名 5-已报名 6-活动已结束(已报名)")
    private Integer signState;

    @Schema(description = "用户是否报名")
    private Boolean userSign;

    @Schema(description = "是否已支付")
    private Boolean pay;

    @Schema(description = "是否已完赛")
    private Boolean finished;
}
