package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 新学员申请请求
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class SubmitStudentApplicationReq {
    @Schema(description = "教练ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long coachId;

    @Schema(description = "申请原因")
    private String reason;

    @Schema(description = "来源 0-默认 1-APP 2-H5", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer source;
}
