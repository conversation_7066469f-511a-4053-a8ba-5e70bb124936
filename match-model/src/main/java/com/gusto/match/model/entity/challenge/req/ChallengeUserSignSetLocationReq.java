package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;

/**
 * 设置挑战地点
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
public class ChallengeUserSignSetLocationReq {

    @Schema(description = "赛季ID")
    @Positive
    private Long seasonId;

    @Schema(description = "挑战地点坐标")
    @NotEmpty
    private String challengeCoordinate;

    @Schema(description = "挑战地点名称")
    @NotEmpty
    private String challengeLocation;

}
