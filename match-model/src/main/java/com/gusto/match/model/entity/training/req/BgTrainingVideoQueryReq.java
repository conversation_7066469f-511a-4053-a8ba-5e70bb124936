package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练视频后台查询类
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Data

public class BgTrainingVideoQueryReq {

    @Schema(description = "字段ID，1=视频名称，2=视频ID")
    private Integer fieldId;

    @Schema(description = "字段值")
    private Object fieldValue;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "是否可见，false=隐藏，true=可见")
    private Boolean visible;

}
