package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛项目报名信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("offline_project_sign_info")
public class OfflineProjectSignInfo extends BaseEntity {

    private static final long serialVersionUID = 8538696386412167577L;

    @Schema(description = "项目ID")
    @TableId(type = IdType.INPUT)
    private Long projectId;

    @Schema(description = "已报名人数，已支付才算")
    private Integer signCount;

    @Schema(description = "乐观锁版本")
    @Version
    private Integer version;

}
