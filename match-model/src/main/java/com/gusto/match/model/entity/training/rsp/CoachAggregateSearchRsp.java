package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.ActivitySearchElementDTO;
import com.gusto.match.model.entity.training.dto.CoachSearchElementDTO;
import com.gusto.match.model.entity.training.dto.TrainingArticleDTO;
import com.gusto.match.model.entity.training.dto.TrainingVideoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 训练聚合搜索响应
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data

public class CoachAggregateSearchRsp {
    @Schema(description = "教练列表")
    private List<CoachSearchElementDTO> coachList;

    @Schema(description = "教练是否包含更多")
    private Integer coachSize;

    @Schema(description = "活动列表")
    private List<ActivitySearchElementDTO> activityList;

    @Schema(description = "活动是否包含更多")
    private Integer activitySize;

    @Schema(description = "视频列表")
    private List<TrainingVideoDTO> videoList;

    @Schema(description = "视频是否包含更多")
    private Integer videoSize;

    @Schema(description = "文章列表")
    private List<TrainingArticleDTO> articleList;

    @Schema(description = "文章是否包含更多")
    private Integer articleSize;

    @Schema(description = "评测列表")
    private List<TrainingArticleDTO> reviewList;

    @Schema(description = "评测是否包含更多")
    private Integer reviewSize;
}
