package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 训练活动
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data

public class TrainingActivityDTO {
    @Schema(description = "训练活动ID")
    private Long activityId;

    @Schema(description = "封面")
    private String coverImage;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "地点类型：0-默认 1-线上 2-线下")
    private Integer locationType;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "人数限制：0为不限制")
    private Integer numberLimit;

    @Schema(description = "已报名人数")
    private Integer signCount;

    @Schema(description = "状态 0-默认 1-进行中 2-报名中 3-已经结束")
    private Integer activityState;
}
