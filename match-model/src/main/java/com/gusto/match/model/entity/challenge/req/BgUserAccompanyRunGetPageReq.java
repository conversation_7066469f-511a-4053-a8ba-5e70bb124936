package com.gusto.match.model.entity.challenge.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 查询用户报名
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
@Data
public class BgUserAccompanyRunGetPageReq {

    @Schema(description = "赛季ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seasonId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "完赛状态：0-默认 1-未挑战 2-挑战成功 3-挑战失败")
    private Integer finishedState;

    @Schema(description = "current")
    private Long current;

    @Schema(description = "size")
    private Long size;

}
