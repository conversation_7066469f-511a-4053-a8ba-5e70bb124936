package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户发起的陪跑记录请求
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class GetUserCreateAccompanyRunRsp {

    @Schema(description = "记录ID")
    private Long recordId;

    @Schema(description = "活动封面")
    private String coverImage;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点名称")
    private String location;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "已报名的人数")
    private Integer signCount;

    @Schema(description = "活动详情图片")
    private String activityImage;

}
