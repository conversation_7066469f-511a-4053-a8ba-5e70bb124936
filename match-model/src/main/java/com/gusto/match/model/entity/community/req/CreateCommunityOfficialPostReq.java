package com.gusto.match.model.entity.community.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 创建官方动态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
public class CreateCommunityOfficialPostReq {

    @Schema(description = "话题ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long topicId;

    @Schema(description = "标题，限制20字")
    @Size(max = 20)
    private String title = "";

    @Schema(description = "内容，限制1000字", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    @Size(max = 1000)
    private String content;

    @Schema(description = "图片列表，限制9张")
    @Size(max = 9)
    private List<String> imageList = Collections.emptyList();

    @Schema(description = "封面图高度")
    private Double imageHeight = 0.0;

    @Schema(description = "封面图宽度")
    private Double imageWidth = 0.0;

    @Schema(description = "关联链接")
    private String url = "";

    @Schema(description = "标签，限制3个")
    @Size(max = 3)
    private List<Long> tagIdList = Collections.emptyList();

}
