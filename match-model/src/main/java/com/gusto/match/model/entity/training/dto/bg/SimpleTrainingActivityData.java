package com.gusto.match.model.entity.training.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class SimpleTrainingActivityData {
    @Schema(description = "排名")
    private Integer rank;

    @Schema(description = "训练活动名称")
    private String name;

    @Schema(description = "已报名人数")
    private Integer signCount;
}
