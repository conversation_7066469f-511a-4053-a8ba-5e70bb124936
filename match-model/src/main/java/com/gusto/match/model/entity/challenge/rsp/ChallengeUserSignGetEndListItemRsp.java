package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户报名列表
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
public class ChallengeUserSignGetEndListItemRsp {

    @Schema(description = "挑战赛ID")
    private Long matchId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "报名ID")
    private Long signId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "背景图")
    private String backgroundImage;

    @Schema(description = "头图-挑战成功")
    private String headImageForSuccess;

    @Schema(description = "头图-挑战失败")
    private String headImageForFailure;

    @Schema(description = "卡片标题图")
    private String cardTitleImage;

    @Schema(description = "用户是否提交报名")
    private Boolean userSign;

    @Schema(description = "是否已完赛")
    private Boolean finished;

    @Schema(description = "目标距离，单位米")
    private Double targetDistance;

    @Schema(description = "实际距离，单位米")
    private Double actualDistance;

    @Schema(description = "完赛用时")
    private String finishedDuration;

    @Schema(description = "完赛时间，单位毫秒")
    private Long finishedTime;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "完整号码")
    private String fullBibno;

    @Schema(description = "报名套餐名称")
    private String signPackageName;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "赛季别名")
    private String seasonAlias;

    @Schema(description = "徽章-点亮")
    private String badgeLightImage;

    @Schema(description = "徽章-未点亮")
    private String badgeDarkImage;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "赛季名称")
    private String seasonName;

    @Schema(description = "报名开始时间")
    private Long signStartTime;

    @Schema(description = "报名截止时间")
    private Long signEndTime;

    @Schema(description = "支付状态：0-默认 1-未支付 2-已支付 3-已退款 4-已部分退款")
    private Integer payState;

}
