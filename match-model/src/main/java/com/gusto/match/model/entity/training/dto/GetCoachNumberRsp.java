package com.gusto.match.model.entity.training.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取万名教练计划成员编号响应
 *
 * <AUTHOR>
 * @since 2021/09/02
 */
@Data

public class GetCoachNumberRsp {
    @Schema(description = "教练编号")
    private String coachNo;

    @Schema(description = "计划名字")
    private String coachProgram;

    @Schema(description = "计划ID")
    private Long planId;
}
