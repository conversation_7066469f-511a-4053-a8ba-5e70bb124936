package com.gusto.match.model.entity.training.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-训练视频统计数据 响应类
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Data

public class TrainingVideoStaticRsp {

    @Schema(description = "视频播放次数")
    private Long view;

    @Schema(description = "视频完整播放次数")
    private Long play;

    @Schema(description = "视频分享次数")
    private Long share;

    @Schema(description = "视频播放人数")
    private Long userSum;

    @Schema(description = "视频总数")
    private Long videoSum;
}

