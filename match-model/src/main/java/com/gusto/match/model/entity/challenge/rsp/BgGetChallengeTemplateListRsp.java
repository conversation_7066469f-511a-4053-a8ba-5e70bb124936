package com.gusto.match.model.entity.challenge.rsp;

import com.gusto.match.model.entity.online.dto.BgOnlineTemplateListElementDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 后台-挑战赛模板列表元素
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Data
public class BgGetChallengeTemplateListRsp {

    @Schema(description = "证书模板")
    private List<BgOnlineTemplateListElementDTO> certList;

    @Schema(description = "号码布模板")
    private List<BgOnlineTemplateListElementDTO> bibnoList;

}
