package com.gusto.match.model.entity.community.rsp;

import com.gusto.match.model.entity.community.dto.BgCommunityBlockDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分页获取用户列表
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class BgQueryCommunityUserRsp {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "发布动态数")
    private Integer postCount;

    @Schema(description = "点击量")
    private Integer clickCount;

    @Schema(description = "点赞量")
    private Integer likeCount;

    @Schema(description = "评论量")
    private Integer commentCount;

    @Schema(description = "分享量")
    private Integer shareCount;

    @Schema(description = "最后发布时间")
    private Long lastPostTime;

    @Schema(description = "是否禁言")
    private Boolean block;

    @Schema(description = "禁言记录列表")
    private List<BgCommunityBlockDTO> blockList;

}
