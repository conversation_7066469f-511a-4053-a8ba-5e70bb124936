package com.gusto.match.model.entity.sunshine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户赛事成绩（个人限时赛）-跑步记录关联
 *
 * <AUTHOR>
 * @since 2021-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sunshine_user_time_result_relation")
public class SunshineUserTimeResultRelation extends BaseEntity {
    private static final long serialVersionUID = -8479171468243664419L;

    @Schema(description = "关联ID")
    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    @Schema(description = "成绩ID")
    private Long resultId;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;
}
