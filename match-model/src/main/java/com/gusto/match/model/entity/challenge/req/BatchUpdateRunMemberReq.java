package com.gusto.match.model.entity.challenge.req;

import com.gusto.match.model.entity.match.req.UpdateRunMemberItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量更新陪跑名单
 *
 * <AUTHOR>
 * @since 2021-07-02
 */
@Data

public class BatchUpdateRunMemberReq {
    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "子项列表")
    private List<UpdateRunMemberItem> itemList;
}
