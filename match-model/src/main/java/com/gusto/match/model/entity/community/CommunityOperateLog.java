package com.gusto.match.model.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 社区操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("community_operate_log")
public class CommunityOperateLog extends BaseEntity {

    private static final long serialVersionUID = 3688828354936480994L;

    @Schema(description = "日志ID")
    @TableId(type = IdType.AUTO)
    private Long logId;

    @Schema(description = "动态ID")
    private Long postId;

    @Schema(description = "操作人类型：0-默认 1-用户 2-管理员 3-系统")
    private Integer operatorType;

    @Schema(description = "操作人ID")
    private Long operatorId;

    @Schema(description = "操作人名称")
    private String operatorName;

    @Schema(description = "对象类型：0-默认 1-动态 2-评论")
    private Integer objectType;

    @Schema(description = "日志类型：0-默认 1-删除 2-恢复 3-限流 4-审核不通过")
    private Integer logType;

    @Schema(description = "对象ID")
    private Long objectId;

    @Schema(description = "来源：0-默认 1-前台 2-后台 3-系统")
    private Integer source;

    @Schema(description = "审核不通过原因")
    private String rejectReason;

}
