package com.gusto.match.model.entity.training.dto.bg;

import com.gusto.match.model.entity.app.rsp.BgContentEventRsp;
import com.gusto.match.model.entity.training.rsp.TrainingArticleStaticRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 后台文章数据
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data

public class BgTrainingArticleData {

    @Schema(description = "文章数据")
    private TrainingArticleStaticRsp articleStatic;

    @Schema(description = "文章浏览列表")
    private List<BgContentEventRsp> articleList;
}
