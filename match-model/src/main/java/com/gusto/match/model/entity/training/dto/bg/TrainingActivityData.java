package com.gusto.match.model.entity.training.dto.bg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 活动数据
 *
 * <AUTHOR>
 * @since 2022/5/5
 */
@Data

public class TrainingActivityData {
    @Schema(description = "活动数量")
    private Long activityAmount;

    @Schema(description = "发活动的教练数量")
    private Long postedActivityCoachAmount;

    @Schema(description = "没有发活动的教练数量")
    private Long unPostedActivityCoachAmount;

    @Schema(description = "活动信息列表")
    private IPage<SimpleTrainingActivityData> activityDataList;
}
