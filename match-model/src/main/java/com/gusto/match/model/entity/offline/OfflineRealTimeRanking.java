package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 线下赛实时排名
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_real_time_ranking")
public class OfflineRealTimeRanking extends BaseTenantEntity {

    private static final long serialVersionUID = -2094578336915207186L;

    @Schema(description = "唯一ID")
    @TableId(type = IdType.INPUT)
    private String uniqueId;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型1：1-string 2-int 3-double")
    private Integer type1;

    @Schema(description = "值1")
    private String value1;

    @Schema(description = "类型2：1-string 2-int 3-double")
    private Integer type2;

    @Schema(description = "值2")
    private String value2;

    @Schema(description = "类型3：1-string 2-int 3-double")
    private Integer type3;

    @Schema(description = "值3")
    private String value3;

    @Schema(description = "榜单类型：0-总榜 1-全马 2-半马")
    private Integer rankType;

}
