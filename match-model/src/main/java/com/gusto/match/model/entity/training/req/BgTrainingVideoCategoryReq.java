package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 训练视频类目后台更新创建请求类
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data

public class BgTrainingVideoCategoryReq {

    @Schema(description = "训练视频类目ID，更新时必穿，为0时表示更新")
    private Long categoryId;

    @Schema(description = "训练视频类目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;
}
