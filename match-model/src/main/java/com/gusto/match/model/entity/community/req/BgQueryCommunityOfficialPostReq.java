package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取官方动态列表
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
@Data
public class BgQueryCommunityOfficialPostReq {

    @Schema(description = "话题ID")
    private Long topicId;

    @Schema(description = "标签ID")
    private Long tagId = 0L;

    @Schema(description = "排序类型：0-默认 1-最新 2-点赞最多 3-评论最多 4-分享最多 5-浏览最多")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sortType = 0;

    @Schema(description = "current")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long current = 1L;

    @Schema(description = "size")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long size = 20L;

}
