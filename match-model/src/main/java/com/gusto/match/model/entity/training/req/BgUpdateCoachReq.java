package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/8
 */
@Data

public class BgUpdateCoachReq {
    @Schema(description = "教练ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> coachIds;

    @Schema(description = "教练等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;
}
