package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/8
 */
@Data

public class BgQueryCoachReq {
    public static final int SKT_USERNAME = 0;
    public static final int SKT_MOBILE = 1;
    public static final int SKT_COACH_NUMBER = 2;
    public static final int SKT_AREA = 3;

    @Schema(description = "关键字类型：-1-全部 0-姓名 1-手机号 2-教练编号 3-地区", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer searchKeyType;

    @Schema(description = "关键字")
    private String searchKey;

    @Schema(description = "教练等级 0-所有 1-准教练 2-初级教练 3-中级教练 4-高级教练", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coachLevel;

    @Schema(description = "线下培训班 0-所有", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coachOfficialPlan;

    @Schema(description = "推荐 0-所有 1-推荐", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer recommend;
}
