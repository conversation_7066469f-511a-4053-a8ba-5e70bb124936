package com.gusto.match.model.entity.offline;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseTenantEntity;
import com.gusto.match.model.mybatis.ListOfflineInviteCodeBatchInvalidItemJsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 线下赛邀请码批量作废任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "offline_invite_code_batch_invalid_job", autoResultMap = true)
public class OfflineInviteCodeBatchInvalidJob extends BaseTenantEntity {

    private static final long serialVersionUID = 7741725022662694263L;

    @Schema(description = "任务ID")
    @TableId(type = IdType.AUTO)
    private Long jobId;

    @Schema(description = "生成类型：0-默认 1-普通 2-全局")
    private Integer genType;

    @Schema(description = "赛事ID")
    private Long raceId;

    @Schema(description = "任务状态：0-默认 1-进行中 2-成功 3-失败")
    private Integer state;

    @Schema(description = "导入总数")
    private Integer totalCount;

    @Schema(description = "成功导入的数量")
    private Integer successCount;

    @Schema(description = "导入失败的作废列表")
    @TableField(typeHandler = ListOfflineInviteCodeBatchInvalidItemJsonTypeHandler.class)
    private List<OfflineInviteCodeBatchInvalidItem> errorInvalidList;

}
