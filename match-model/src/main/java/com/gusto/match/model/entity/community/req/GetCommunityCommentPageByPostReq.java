package com.gusto.match.model.entity.community.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * <p>
 * 根据动态分页获取评论列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class GetCommunityCommentPageByPostReq {

    @Parameter(description = "动态ID")
    @Positive
    private Long postId;

    @Parameter(description = "排序类型：0-默认 1-最热 2-最新")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sortType = 1;

    @Parameter(description = "current")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long current = 1L;

    @Parameter(description = "size")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long size = 10L;

}
