package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.survey.dto.FormField;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/20
 */
public class ListFormFieldJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<FormField>> typeReference = new TypeReference<>() {
    };

    public ListFormFieldJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
