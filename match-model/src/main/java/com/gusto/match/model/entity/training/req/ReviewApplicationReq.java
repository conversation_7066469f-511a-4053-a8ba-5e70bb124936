package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 审核申请记录请求
 *
 * <AUTHOR>
 * @since 2021/8/18
 */
@Data

public class ReviewApplicationReq {
    @Schema(description = "申请记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long recordId;

    @Schema(description = "状态：2-已同意 3-已拒绝", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer state;

    @Schema(description = "拒绝信息")
    private String message;
}
