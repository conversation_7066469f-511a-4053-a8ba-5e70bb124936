package com.gusto.match.model.entity.challenge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.match.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 用户陪跑成员
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("match_user_accompany_run_member")
public class UserAccompanyRunMember extends BaseEntity {

    private static final long serialVersionUID = 4884261931352626850L;

    @Schema(description = "成员ID")
    @TableId(value = "member_id", type = IdType.AUTO)
    private Long memberId;

    @Schema(description = "陪跑记录ID")
    private Long recordId;

    @Schema(description = "赛季ID")
    private Long seasonId;

    @Schema(description = "成员用户ID")
    private Long userId;

    @Schema(description = "姓名")
    private String username;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "报名时间")
    private Instant signTime;

    @Schema(description = "实际贡献距离，单位米")
    private Double distance;

    @Schema(description = "陪跑方式 0-默认 1-到场 2-异地")
    private Integer accompanyMode;

    @Schema(description = "陪跑记录用户ID")
    @TableField(exist = false)
    private Long recordUserId;

    @Schema(description = "挑战失败的鼓励")
    private String encourage;

    @Schema(description = "总跑团ID")
    private Long headClubId;

}
