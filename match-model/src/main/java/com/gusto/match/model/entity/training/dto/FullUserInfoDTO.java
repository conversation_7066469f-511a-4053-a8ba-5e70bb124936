package com.gusto.match.model.entity.training.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/9/9
 */
// 完全按照旧版数据名字返回
@Data

public class FullUserInfoDTO {
   /* @Schema(description = "胸围")
    private Double bust;
    // 计算
    @Schema(description = "粉丝")
    private Long fans;
    @Schema(description = "最大心率")
    private Double maxheartrate;
    @Schema(description = "介绍")
    private String signature;
    @Schema(description = "跑者等级")
    private Integer grade;
    @Schema(description = "用户ID")
    private Long uuid;
    @Schema(description = "性别")
    private Integer sex;
    @Schema(description = "体脂")
    private Double bfr;
    @Schema(description = "臀围")
    private Double hips;
    @Schema(description = "手机")
    private String mobile;
    @Schema(description = "是否已绑定微信 1-已绑定 0-未绑定")
    private Integer wechat;
    @Schema(description = "unionId")
    private String unionid;
    @Schema(description = "身高cm")
    private Double height;
    @Schema(description = "体重kg")
    private Double weight;
    @Schema(description = "苹果账号")
    private String appleid;
    @Schema(description = "市")
    private String city;
    @Schema(description = "头像")
    private String avatar;
    // 计算
    @Schema(description = "关注")
    private Long follow;
    @Schema(description = "昵称")
    private String username;
    //计算
    @Schema(description = "点赞")
    private Long praise;
    @Schema(description = "省")
    private String province;
    @Schema(description = "生日")
    private Instant birthday;
    @Schema(description = "bmi")
    private Double bmi;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "心率")
    private Double heartrate;
    // 计算
    @Schema(description = "好友")
    private Long friends;

    @Schema(description = "介绍")
    private String description;

    @Schema(description = "教练人数")
    private Integer coachCount;

    @Schema(description = "学员人数")
    private Integer studentCount;

    @Schema(description = "跑龄，单位月")
    private Long runMonths;

    @Schema(description = "是否是教练")
    private Boolean isCoach;

    // 计算
    @Schema(description = "关注")
    private Long follows;

    @Schema(description = "是否测试账号")
    private Integer testFight;

    @Schema(description = "微信名字")
    private String wechatName;*/
}
