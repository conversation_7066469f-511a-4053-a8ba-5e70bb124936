package com.gusto.match.model.entity.challenge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 挑战赛项目名称
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class ProjectNameDTO {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "名称")
    private String title;

    @Schema(description = "封面图片")
    private String coverImage;

    @Schema(description = "项目开始时间")
    private Long startTime;

    @Schema(description = "项目截止时间")
    private Long endTime;

}
