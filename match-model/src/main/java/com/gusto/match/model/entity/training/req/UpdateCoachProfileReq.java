package com.gusto.match.model.entity.training.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 修改教练信息请求
 *
 * <AUTHOR>
 * @since 2021/8/31
 */
@Data

public class UpdateCoachProfileReq {
    @Schema(description = "介绍")
    private String description;

    @Schema(description = "获奖经历")
    private List<String> awards;

    @Schema(description = "相关资质")
    private List<String> qualifications;

    @Schema(description = "是否展现自己的训练数据")
    private Boolean hide;
}
