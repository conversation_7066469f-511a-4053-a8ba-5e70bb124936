package com.gusto.match.model.entity.challenge.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 获取陪跑总里程榜
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
public class ChallengeRankGetAccompanyDistanceRsp implements Serializable {

    private static final long serialVersionUID = 4101475375687680507L;

    @Schema(description = "是否发起过陪跑")
    private Boolean accompany;

    @Schema(description = "本人排名")
    private ChallengeRankGetAccompanyItemRsp currentRank;

    @Schema(description = "排名列表")
    private List<ChallengeRankGetAccompanyItemRsp> rankList;

}
