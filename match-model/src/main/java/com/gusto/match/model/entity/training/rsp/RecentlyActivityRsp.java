package com.gusto.match.model.entity.training.rsp;

import com.gusto.match.model.entity.training.dto.TrainingActivityListItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Data

public class RecentlyActivityRsp {

    @Schema(description = "最新的训练活动")
    private List<TrainingActivityListItemDTO> newActivityList;

    @Schema(description = "正在进行中活动")
    private List<TrainingActivityListItemDTO> onGoingActivityList;
}
