<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gusto.match</groupId>
        <artifactId>match-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>match-model</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.gusto.framework</groupId>
            <artifactId>gusto-core</artifactId>
            <version>${gusto-framework.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>springdoc-openapi-common</artifactId>
                    <groupId>org.springdoc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springdoc-openapi-ui</artifactId>
                    <groupId>org.springdoc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springdoc-openapi-webmvc-core</artifactId>
                    <groupId>org.springdoc</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gusto.push</groupId>
            <artifactId>push-common-model</artifactId>
            <version>${push-common-model.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 中间件模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- Forest Java HTTP 客户端框架, 在线文档：http://forest.dtflyx.com/docs/ -->
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
            <version>${forest.version}</version>
        </dependency>

        <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>
        <!-- Sa-Token整合redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-jackson</artifactId>
        </dependency>

        <!-- 多级缓存 -->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
        </dependency>

        <!-- EasyPoi -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>${easypoi.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>easypoi-wps</artifactId>
                    <groupId>cn.afterturn</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- XSS过滤 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>

        <!-- 微信开发平台 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 支付宝 -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>2.2.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 拼音生成 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
        </dependency>

        <!-- 定时任务 -->
        <dependency>
            <groupId>org.apache.shardingsphere.elasticjob</groupId>
            <artifactId>elasticjob-lite-spring-boot-starter</artifactId>
            <version>${elasticjob-lite.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.3</version>
        </dependency>

        <!-- 七牛云 -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <!-- aes 对称加密 PKCS7Padding 模式 https://doc.hutool.cn/pages/SymmetricCrypto/#aes%E5%B0%81%E8%A3%85 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.68</version>
        </dependency>

        <dependency>
            <groupId>io.github.mweirauch</groupId>
            <artifactId>micrometer-jvm-extras</artifactId>
            <version>0.2.2</version>
        </dependency>

        <!-- Emoji -->
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>${emoji.version}</version>
        </dependency>

        <!-- https://github.com/springdoc/springdoc-openapi/pull/2291 -->
        <dependency>
            <groupId>com.github.eshizhan.springdoc-openapi</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>e986414ba5</version>
        </dependency>
        <dependency>
            <groupId>com.github.eshizhan.springdoc-openapi</groupId>
            <artifactId>springdoc-openapi-webmvc-core</artifactId>
            <version>e986414ba5</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.8.0</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-webmvc-core</artifactId>
            <version>1.8.0</version>
        </dependency>

        <!-- 阿里云内容安全 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>green20220302</artifactId>
            <version>2.2.8</version>
        </dependency>

        <!-- 个推 -->
        <dependency>
            <groupId>com.getui.push</groupId>
            <artifactId>restful-sdk</artifactId>
            <version>1.0.6.0</version>
        </dependency>

        <!-- PayPal -->
        <dependency>
            <groupId>com.paypal.sdk</groupId>
            <artifactId>paypal-server-sdk</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>core</artifactId>
                    <groupId>io.apimatic</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.apimatic</groupId>
            <artifactId>core</artifactId>
            <version>0.6.13</version>
        </dependency>
    </dependencies>
</project>
