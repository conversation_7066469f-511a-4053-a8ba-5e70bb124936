# 相册管理模块

## 概述

相册管理模块用于管理线下赛事的摄影相册，支持与谱时相册系统的集成，提供照片销售和分成管理功能。

## 功能特性

### 1. 相册管理
- 创建、更新、删除相册
- 关联线下赛事
- 配置谱时相册ID列表
- 设置摄影师和公司分成比例
- 设置单张照片价格
- 相册状态管理（启用/禁用）
- 相册排序权重设置

### 2. 折扣配置
- 支持多种折扣类型：按比例折扣、固定价格折扣
- 灵活的数量阶梯折扣（如3张9折、5张8折、10张7折）
- 折扣配置的启用/禁用管理
- 自动选择最优折扣

### 3. 价格计算
- 实时计算照片购买总价
- 自动应用最优折扣
- 支持原价和折扣价对比显示

## 数据模型

### PhotoAlbum（相册表）
```sql
CREATE TABLE `photo_album` (
  `album_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '相册ID',
  `offline_race_id` bigint(20) NOT NULL COMMENT '关联的线下赛赛事ID',
  `album_name` varchar(100) NOT NULL COMMENT '相册名称',
  `album_description` varchar(500) DEFAULT NULL COMMENT '相册描述',
  `photo_plus_album_ids` json NOT NULL COMMENT '谱时相册ID列表',
  `photographer_share_ratio` decimal(5,2) NOT NULL COMMENT '摄影师分成比例（百分比）',
  `company_share_ratio` decimal(5,2) NOT NULL COMMENT '公司分成比例（百分比）',
  `single_photo_price` decimal(10,2) NOT NULL COMMENT '单张照片价格（元）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '相册状态：0-禁用 1-启用',
  `cover_image_url` varchar(500) DEFAULT NULL COMMENT '相册封面图片URL',
  `sort_weight` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  -- 其他公共字段...
);
```

### PhotoAlbumDiscount（相册折扣配置表）
```sql
CREATE TABLE `photo_album_discount` (
  `discount_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '折扣配置ID',
  `album_id` bigint(20) NOT NULL COMMENT '关联的相册ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `discount_ratio` decimal(5,2) DEFAULT NULL COMMENT '折扣比例（百分比）',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣后单价（元）',
  `discount_type` tinyint(1) NOT NULL COMMENT '折扣类型：1-按比例折扣 2-固定价格',
  `discount_name` varchar(50) DEFAULT NULL COMMENT '折扣名称',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用 1-启用',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序权重',
  -- 其他公共字段...
);
```

## API接口

### 管理后台接口

#### 1. 分页查询相册列表
```
GET /photo/album/query
```

#### 2. 获取相册详情
```
GET /photo/album/detail/{albumId}
```

#### 3. 创建相册
```
POST /photo/album/create
```

#### 4. 更新相册
```
PUT /photo/album/update
```

#### 5. 删除相册
```
DELETE /photo/album/delete/{albumId}
```

#### 6. 启用/禁用相册
```
PUT /photo/album/status/{albumId}?status={0|1}
```

#### 7. 创建折扣配置
```
POST /photo/album/discount/create
```

#### 8. 启用/禁用折扣配置
```
PUT /photo/album/discount/status/{discountId}?status={0|1}
```

#### 9. 删除折扣配置
```
DELETE /photo/album/discount/delete/{discountId}
```

### 用户端接口

#### 1. 获取线下赛相册列表
```
GET /photo/album/list?offlineRaceId={raceId}
```

#### 2. 获取相册详情
```
GET /photo/album/detail/{albumId}
```

#### 3. 计算照片价格
```
POST /photo/album/calculate-price
{
  "albumId": 1,
  "quantity": 3
}
```

## 业务规则

### 1. 分成比例验证
- 摄影师分成比例 + 公司分成比例 = 100%
- 分成比例必须在0-100之间

### 2. 折扣计算规则
- 系统自动选择符合购买数量的最优折扣
- 按比例折扣：总价 = 原价 × 数量 × 折扣比例
- 固定价格折扣：总价 = 折扣单价 × 数量

### 3. 相册状态管理
- 只有启用状态的相册才会在用户端显示
- 禁用的相册不参与价格计算

### 4. 折扣优先级
- 购买数量达到多个折扣条件时，选择数量要求最高的折扣
- 例如：购买5张照片，有3张9折和5张8折两个折扣，选择5张8折

## 集成说明

### 与谱时系统集成
- 通过 `photo_plus_album_ids` 字段关联谱时相册
- 支持一个相册关联多个谱时相册ID
- 可通过谱时API获取相册图片信息

### 与线下赛系统集成
- 通过 `offline_race_id` 字段关联线下赛事
- 支持按赛事查询相册列表
- 继承赛事的租户信息

## 使用示例

### 创建相册示例
```java
PhotoAlbum album = new PhotoAlbum();
album.setOfflineRaceId(1L);
album.setAlbumName("2025北京马拉松相册");
album.setPhotoPlusAlbumIds(Arrays.asList("album001", "album002"));
album.setPhotographerShareRatio(BigDecimal.valueOf(30));
album.setCompanyShareRatio(BigDecimal.valueOf(70));
album.setSinglePhotoPrice(BigDecimal.valueOf(15.00));

Long albumId = photoAlbumService.createAlbum(album);
```

### 创建折扣配置示例
```java
List<PhotoAlbumDiscount> discounts = Arrays.asList(
    createDiscount(3, 90, "3张9折"),
    createDiscount(5, 80, "5张8折"),
    createDiscount(10, 70, "10张7折")
);

photoAlbumDiscountService.createDiscounts(albumId, discounts);
```

### 价格计算示例
```java
// 购买5张照片，原价15元/张
BigDecimal totalPrice = photoAlbumDiscountService.calculateTotalPrice(
    albumId, 5, BigDecimal.valueOf(15.00)
);
// 结果：60.00（75 * 0.8 = 60，使用5张8折优惠）
```

## 注意事项

1. 相册删除会级联删除所有关联的折扣配置
2. 分成比例修改需要确保总和为100%
3. 折扣配置支持热更新，修改后立即生效
4. 建议为相册设置合理的排序权重以控制显示顺序
5. 相册封面图片URL需要确保可访问性
