package com.gusto.match.core.dao.photo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gusto.match.model.entity.photo.PhotoAlbum;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 相册管理
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
public interface PhotoAlbumDao extends BaseMapper<PhotoAlbum> {

    /**
     * 根据线下赛ID查询相册列表
     *
     * @param offlineRaceId 线下赛ID
     * @return 相册列表
     */
    List<PhotoAlbum> selectByOfflineRaceId(@Param("offlineRaceId") Long offlineRaceId);

    /**
     * 根据线下赛ID和状态查询相册列表
     *
     * @param offlineRaceId 线下赛ID
     * @param status 状态
     * @return 相册列表
     */
    List<PhotoAlbum> selectByOfflineRaceIdAndStatus(@Param("offlineRaceId") Long offlineRaceId, @Param("status") Integer status);

    /**
     * 根据谱时相册ID查询相册
     *
     * @param photoPlusAlbumId 谱时相册ID
     * @return 相册信息
     */
    PhotoAlbum selectByPhotoPlusAlbumId(@Param("photoPlusAlbumId") String photoPlusAlbumId);
}
