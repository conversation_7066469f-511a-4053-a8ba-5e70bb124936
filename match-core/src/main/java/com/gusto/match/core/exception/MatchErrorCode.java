package com.gusto.match.core.exception;

import com.gusto.framework.core.exception.ErrorCode;

/**
 * 错误码 10000-10999 为保留错误码，11000之后为业务错误码，每个模块用前面两位数区分
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
public interface MatchErrorCode {

    ErrorCode OBJECT_NOT_EXISTS = new ErrorCode(10000, "对象不存在");
    ErrorCode OPERATION_DUPLICATE = new ErrorCode(10001, "请勿重复提交", "Please do not submit repeatedly");
    ErrorCode OPERATION_FAILED = new ErrorCode(10002, "提交失败，请稍后再试", "Submission failed, please try again later");
    ErrorCode PARAMS_NOT_VALID = new ErrorCode(10003, "参数校验错误");
    ErrorCode ILLEGAL_IP = new ErrorCode(10004, "非法IP");
    ErrorCode INVALID_SIGN = new ErrorCode(10005, "非法签名");
    ErrorCode ILLEGAL_DATA = new ErrorCode(10006, "用户提交包含敏感信息");
    ErrorCode OBJECT_NOT_BELONG_USER = new ErrorCode(10007, "对象不属于当前用户");

    // 后台管理员
    ErrorCode ADMIN_NOT_EXISTS = new ErrorCode(11000, "管理员不存在");
    ErrorCode ADMIN_ROLE_NOT_EXISTS = new ErrorCode(11001, "管理员角色不存在");
    ErrorCode ADMIN_NAME_EXISTS = new ErrorCode(11002, "管理员名称已重复");
    ErrorCode ADMIN_ROLE_NAME_EXISTS = new ErrorCode(11003, "管理员角色名称已重复");
    ErrorCode ADMIN_PASSWD_MISTAKE = new ErrorCode(11004, "管理员登录密码错误");
    ErrorCode ADMIN_IS_LOCK = new ErrorCode(11005, "此账号已被锁定，如有疑问，请联系管理员");
    ErrorCode ADMIN_MODIFY_OTHER = new ErrorCode(11006, "禁止非超级管理员修改其他管理员的信息");
    ErrorCode ADMIN_CREATE_SUPER = new ErrorCode(11007, "禁止非超级管理员创建超级管理员");
    ErrorCode ADMIN_LOGIN_ERROR = new ErrorCode(11008, "管理员登录错误");
    ErrorCode ADMIN_LOGIN_TIMEOUT = new ErrorCode(11009, "管理员登录过期");
    ErrorCode ADMIN_LOGIN_REPLACED = new ErrorCode(11010, "管理员在其他地方已登录");
    ErrorCode ADMIN_PERMISSION_DENIED = new ErrorCode(11011, "管理员权限不足");
    ErrorCode ADMIN_DISABLE_ERROR = new ErrorCode(11012, "管理员已被封禁");
    ErrorCode ADMIN_MOBILE_EXISTS = new ErrorCode(11013, "管理员手机已重复");
    ErrorCode ADMIN_PASSWD_FORMAT_ERROR = new ErrorCode(11014, "设置密码最小长度为8位，密码由数字、字母和特殊字符组合而成");

    // 挑战赛
    ErrorCode MATCH_NOT_EXISTS = new ErrorCode(12000, "赛事不存在");
    ErrorCode MATCH_SEASON_NOT_EXISTS = new ErrorCode(12001, "赛事赛季不存在");
    ErrorCode MATCH_PROJECT_NOT_EXISTS = new ErrorCode(12002, "赛事项目不存在");
    ErrorCode MATCH_GOODS_NOT_EXISTS = new ErrorCode(12003, "赛事商品不存在");
    ErrorCode MATCH_MEMBER_LIMIT_ERROR = new ErrorCode(12004, "赛事队伍人数大于要求人数");
    ErrorCode MATCH_PROJECT_BIBNO_SET_ERROR = new ErrorCode(12005, "赛事项目号码自增设置错误");
    ErrorCode MATCH_GOODS_BUT_COUNT_LIMIT = new ErrorCode(12006, "赛事商品数量超过单次最大购买数量");
    ErrorCode MATCH_USER_CAN_NOT_JOIN_BY_AGE = new ErrorCode(12007, "青春版仅限20岁以上50岁以下报名");
    ErrorCode MATCH_USER_CAN_NOT_JOIN_BY_AGE_BASE = new ErrorCode(12008, "仅限20岁以上100岁以下报名");
    ErrorCode CHALLENGE_USER_SIGN_SET_TARGET_ERROR = new ErrorCode(12009, "设置挑战目标错误");
    ErrorCode CHALLENGE_USER_SIGN_SUBMIT_ERROR = new ErrorCode(12010, "人生100报名错误");
    ErrorCode CHALLENGE_TEMPLATE_GET_CERT_ERROR = new ErrorCode(12011, "人生100获取证书错误");
    ErrorCode CHALLENGE_PROJECT_UPSERT_ERROR = new ErrorCode(12011, "挑战赛项目新增/更新错误");

    // 用户
    ErrorCode USER_NOT_EXISTS = new ErrorCode(13000, "用户不存在");
    ErrorCode USER_NICKNAME_NOT_LEGAL = new ErrorCode(13001, "昵称不合规，不能含有表情包或者特殊字符");
    ErrorCode USER_ACCOUNT_MERGE_ERROR = new ErrorCode(13002, "用户账号合并错误");
    ErrorCode USER_SIGNATURE_NOT_LEGAL = new ErrorCode(13003, "简介不合规，不能含有表情包或者特殊字符");

    // 用户报名
    ErrorCode USER_SIGN_NOT_EXISTS = new ErrorCode(14000, "用户报名信息不存在");
    ErrorCode USER_SIGN_EXISTS = new ErrorCode(14001, "用户报名信息已存在");
    ErrorCode USER_SIGN_NOT_BELONG_USER = new ErrorCode(14002, "报名信息不属于当前用户");
    ErrorCode USER_SIGN_CANT_CANCEL = new ErrorCode(14003, "已支付报名不能取消");
    ErrorCode USER_SIGN_BIBNO_EXISTS = new ErrorCode(14004, "用户参赛号码重复");
    ErrorCode USER_SIGN_NOT_CONTAIN_REQUIRED_GOODS = new ErrorCode(14005, "用户提交报名没有勾选必选商品");
    ErrorCode USER_SIGN_NOT_CONTAIN_PACKAGE = new ErrorCode(14006, "用户提交报名没有勾选套餐");
    ErrorCode USER_SIGN_PACKAGE_NOT_EXISTS = new ErrorCode(14007, "用户提交报名选择的套餐不存在");
    ErrorCode USER_SIGN_PACKAGE_ILLEGAL = new ErrorCode(14008, "用户提交报名选择的套餐非法");
    ErrorCode USER_SIGN_NOT_PAY = new ErrorCode(14009, "用户报名未支付");
    ErrorCode USER_SIGN_PAID = new ErrorCode(14010, "用户报名已支付");
    ErrorCode USER_SIGN_UPDATE_MAX_BIBNO_ERROR = new ErrorCode(14011, "更新项目最大号码失败，请重试");
    ErrorCode USER_SIGN_UPDATE_ERROR = new ErrorCode(14012, "更新报名失败");

    // 用户报名卡
    ErrorCode USER_CARD_NOT_EXISTS = new ErrorCode(15000, "报名卡不存在");
    ErrorCode USER_CARD_NOT_BELONG_USER = new ErrorCode(15001, "报名卡不属于当前用户");
    ErrorCode USER_CARD_BIRTHDAY_IS_NULL = new ErrorCode(15002, "报名卡生日为空");
    ErrorCode USER_CARD_FIELD_EMPTY = new ErrorCode(15003, "报名信息不完善，请返回，点击报名人，完善报名信息");
    ErrorCode USER_CARD_CARD_NUMBER_ERROR = new ErrorCode(15004, "报名卡证件号码错误");
    ErrorCode USER_CARD_CARD_NAME_ERROR = new ErrorCode(15005, "报名卡姓名不正确");
    ErrorCode USER_CARD_CARD_SEX_ERROR = new ErrorCode(15006, "报名卡性别不正确");

    // 订单
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(16000, "订单不存在", "The order does not exist");
    ErrorCode ORDER_PAY_ERROR = new ErrorCode(16001, "订单支付失败", "Order payment failed");
    ErrorCode ORDER_PAID = new ErrorCode(16002, "订单已支付，请等待页面刷新", "The order has been paid, please wait for the page to refresh");
    ErrorCode ORDER_NOT_BELONG_USER = new ErrorCode(16003, "订单不属于当前用户", "The order does not belong to the current user");
    ErrorCode ORDER_CANT_CANCEL = new ErrorCode(16004, "已支付订单不能取消");
    ErrorCode ORDER_CANT_CONFIRM_GOODS = new ErrorCode(16005, "已发货订单才能确认收货");
    ErrorCode ORDER_NOT_PAY = new ErrorCode(16006, "订单未支付");
    ErrorCode ORDER_PUSHED = new ErrorCode(16007, "订单已经推送");
    ErrorCode ORDER_SHIPPED = new ErrorCode(16008, "订单已发货");
    ErrorCode ORDER_CANCELED = new ErrorCode(16009, "订单已取消");
    ErrorCode ORDER_INCLUDE_SIGN_FEE_CANT_CANCEL = new ErrorCode(16010, "提交报名创建的订单不能取消");
    ErrorCode ORDER_REFUND_ERROR = new ErrorCode(16011, "订单退款失败");
    ErrorCode ORDER_ALREADY_REFUND = new ErrorCode(16012, "订单已经退款");
    ErrorCode ORDER_PAYMENT_RECORD_NOT_EXISTS = new ErrorCode(16013, "支付记录不存在");
    ErrorCode ORDER_GET_PAYMENT_INFO_ERROR = new ErrorCode(16014, "订单获取支付信息失败");
    ErrorCode EXTERNAL_ORDER_ERROR = new ErrorCode(16015, "外部订单错误");

    // 完赛证书 / 号码布
    ErrorCode CERT_GET_ERROR = new ErrorCode(17000, "获取证书失败");
    ErrorCode CERT_NOT_BELONG_USER = new ErrorCode(17001, "证书不属于当前用户");
    ErrorCode CERT_ALREADY_DELETED = new ErrorCode(17002, "证书已删除");
    ErrorCode TEMPLATE_NOT_EXISTS = new ErrorCode(17003, "模板不存在");
    ErrorCode CERT_TEMPLATE_NOT_EXISTS = new ErrorCode(17004, "证书模板不存在");
    ErrorCode CERT_UPDATE_ERROR = new ErrorCode(17005, "证书更新错误");

    // 助力赛
    ErrorCode HELP_ERROR = new ErrorCode(18000, "助力失败");
    ErrorCode HELP_LIST_EMPTY = new ErrorCode(18001, "助力成员为空");
    ErrorCode HELP_NAME_UPDATE_ERROR = new ErrorCode(18002, "助力成员姓名更新失败");
    ErrorCode HELP_CANT_HELP_SELF = new ErrorCode(18003, "不能助力自己");
    ErrorCode HELP_CANT_REPEATED = new ErrorCode(18004, "不能重复助力");
    ErrorCode CHALLENGE_LATEST_IS_ONGOING = new ErrorCode(18008, "上一次挑战未结束");

    // 陪跑
    ErrorCode ACCOMPANY_RUN_NOT_EXISTS = new ErrorCode(19000, "陪跑不存在");
    ErrorCode ACCOMPANY_RUN_END = new ErrorCode(19001, "陪跑已结束");
    ErrorCode ACCOMPANY_RUN_LATEST_NOT_END = new ErrorCode(19002, "上一次陪跑未结束");
    ErrorCode ACCOMPANY_RUN_NOT_BELONG_USER = new ErrorCode(19003, "当前用户非陪跑发起者");
    ErrorCode ACCOMPANY_RUN_MEMBER_EXISTS = new ErrorCode(19004, "陪跑报名信息已存在");
    ErrorCode ACCOMPANY_RUN_MEMBER_NOT_EXISTS = new ErrorCode(19005, "陪跑报名信息不存在");
    ErrorCode ACCOMPANY_RUN_MEMBER_NOT_BELONG_USER = new ErrorCode(19006, "陪跑报名信息不属于当前用户");
    ErrorCode ACCOMPANY_RUN_SIGN_END = new ErrorCode(19007, "陪跑报名时间已截止");
    ErrorCode ACCOMPANY_RUN_REQUIRED_PRESENT = new ErrorCode(19008, "陪跑到场时间必填");
    ErrorCode ACCOMPANY_RUN_SIGN_END_TIME_ILLEGAL = new ErrorCode(19009, "陪跑报名结束时间大于陪跑结束时间");
    ErrorCode ACCOMPANY_RUN_MEMBER_NUMBER_LIMIT = new ErrorCode(19010, "陪跑活动报名人数已达上限");
    ErrorCode ACCOMPANY_CREATE_ERROR = new ErrorCode(19011, "创建陪跑错误");
    ErrorCode ACCOMPANY_UPDATE_ERROR = new ErrorCode(19012, "更新陪跑错误");
    ErrorCode ACCOMPANY_JOIN_ERROR = new ErrorCode(19013, "报名陪跑错误");
    ErrorCode ACCOMPANY_UPDATE_ENCOURAGE_ERROR = new ErrorCode(19014, "更新陪跑鼓励失败");

    // 教练
    ErrorCode COACH_REQUEST_ILLEGAL = new ErrorCode(20000, "教练非法请求");
    ErrorCode COACH_NOT_EXISTS = new ErrorCode(20001, "教练不存在");
    ErrorCode COACH_USER_NOT_FOUND = new ErrorCode(20002, "用户不是教练");
    ErrorCode COACH_LATEST_APPLICATION_NOT_END = new ErrorCode(20003, "上一次申请还在审核");
    ErrorCode COACH_ALREADY = new ErrorCode(20004, "用户已经是教练");
    ErrorCode COACH_APPLICATION_NOT_SUBMIT = new ErrorCode(20005, "用户不是该教练的学员");
    ErrorCode COACH_LIMIT = new ErrorCode(20006, "用户的教练不能多于3个");
    ErrorCode COACH_CANT_BE_SELF_STUDENT = new ErrorCode(20007, "教练不能成为自己的学员");
    ErrorCode COACH_STUDENT_NOT_EXIST = new ErrorCode(20008, "学员不存在");
    ErrorCode COACH_CANT_APPLY_SELF = new ErrorCode(20009, "不能自己申请成为自己的学员");
    ErrorCode COACH_NOT_ALLOW_REVIEW = new ErrorCode(20010, "不能审核不属于自己的学员申请");
    ErrorCode COACH_STUDENT_APPLY_IS_FINISH = new ErrorCode(20011, "学员申请已审核");
    ErrorCode COACH_STUDENT_APPLY_IS_ACCEPT = new ErrorCode(20012, "学员申请已通过");
    ErrorCode COACH_STUDENT_APPLY_NOT_EXIST = new ErrorCode(20013, "学员申请记录不存在");
    ErrorCode COACH_STUDENT_HAS_THREE_COACH = new ErrorCode(20014, "很遗憾，该学员教练已满");
    ErrorCode COACH_OFFICIAL_PLAN_NOT_EXIST = new ErrorCode(20015, "万名教练计划不存在");
    ErrorCode COACH_STUDENT_NOT_MATCH = new ErrorCode(20016, "用户不是该教练的学员");
    ErrorCode COACH_PERMISSION_DENIED = new ErrorCode(20017, "教练权限不足");
    ErrorCode COACH_USER_NOT_RUN_HALF_MARATHON = new ErrorCode(20018, "成为教练需要第一赛道APP的半马成绩哦");
    ErrorCode COACH_LOCKED = new ErrorCode(20019, "教练被锁定");

    // 训练活动
    ErrorCode TRAINING_ACTIVITY_USER_IS_JOIN = new ErrorCode(21000, "用户已参加该训练活动");
    ErrorCode TRAINING_ACTIVITY_USER_NOT_JOIN = new ErrorCode(21001, "用户没有参加该训练活动");
    ErrorCode TRAINING_ACTIVITY_USER_IS_SIGN_IN = new ErrorCode(21002, "用户已经签到");
    ErrorCode TRAINING_ACTIVITY_ALREADY_START = new ErrorCode(21003, "训练活动已开始");
    ErrorCode TRAINING_ACTIVITY_USER_SIGN_IN_OUT = new ErrorCode(21004, "用户不在训练活动签到范围内");
    ErrorCode TRAINING_ACTIVITY_NOT_EXIST = new ErrorCode(21005, "训练活动不存在");
    ErrorCode TRAINING_ACTIVITY_JOIN_LIMIT = new ErrorCode(21006, "训练活动人数到达限制");
    ErrorCode TRAINING_ACTIVITY_NOT_BELONG_USER = new ErrorCode(21007, "训练活动非当前用户创建");
    ErrorCode TRAINING_ACTIVITY_DELETE_NOT_ALLOW = new ErrorCode(21008, "训练活动删除不合法");
    ErrorCode TRAINING_ACTIVITY_CAN_NOT_SIGN_IN = new ErrorCode(21009, "活动开始前1小时才能签到");

    // APP 配置
    ErrorCode APP_GUIDE_NOT_EXIST = new ErrorCode(22000, "权限设置指引不存在");
    ErrorCode APP_LAYOUT_GROUP_ENABLE_MUST_HAVE_ONE = new ErrorCode(22001, "必须存在一个启用中的布局分组");
    ErrorCode INTERFACE_DEPRECATED = new ErrorCode(22003, "接口废弃");

    // 习惯养成
    ErrorCode HABIT_ACTIVITY_NOT_EXIST = new ErrorCode(23000, "习惯活动活动不存在");
    ErrorCode HABIT_USER_SIGN_NOT_EXIST = new ErrorCode(23001, "习惯活动用户报名不存在");
    ErrorCode HABIT_USER_SIGN_NOT_PAID = new ErrorCode(23002, "习惯活动用户报名未支付");
    ErrorCode HABIT_MANUAL_CLOCK_DISTANCE_ILLEGAL = new ErrorCode(23003, "习惯活动打卡距离不合法");
    ErrorCode HABIT_USER_SIGN_EXISTS = new ErrorCode(23004, "习惯活动用户已经报名");
    ErrorCode HABIT_USER_SIGN_NOT_CONTAIN_PACKAGE = new ErrorCode(23005, "习惯活动打卡用户提交报名没有勾选套餐");
    ErrorCode HABIT_USER_SIGN_ILLEGAL = new ErrorCode(23006, "习惯活动报名不合法");
    ErrorCode HABIT_USER_SIGN_NOT_IN_SIGN_TIME = new ErrorCode(23007, "习惯活动报名不在报名时间范围内");
    ErrorCode HABIT_USER_SIGN_NOT_BELONG_USER = new ErrorCode(23008, "习惯活动报名不属于用户");
    ErrorCode HABIT_MANUAL_CLOCK_TIME_ILLEGAL = new ErrorCode(23009, "习惯活动打卡时间不正确");
    ErrorCode HABIT_USER_ACTIVITY_NOT_GOING = new ErrorCode(23010, "习惯活动不在进行中");
    ErrorCode HABIT_USER_NOT_FINISH = new ErrorCode(23011, "习惯活动打卡没有完成");
    ErrorCode HABIT_MANUAL_CLOCK_REPEAT = new ErrorCode(23012, "习惯活动重复打卡");
    ErrorCode HABIT_ACTIVITY_TIME_ERROR = new ErrorCode(23013, "习惯活动时间出现错误");
    ErrorCode HABIT_RESURRECTION_NOT_EXIST = new ErrorCode(23014, "习惯活动复活机制不存在");
    ErrorCode HABIT_ACTIVITY_END = new ErrorCode(23015, "习惯活动已经结束");
    ErrorCode HABIT_RESURRECTION_NOT_ALLOW = new ErrorCode(23016, "习惯活动复活条件不足");
    ErrorCode HABIT_RESURRECTION_KUDOS_END = new ErrorCode(23017, "习惯活动复活点赞结束");
    ErrorCode HABIT_RESURRECTION_KUDOS_EXIST = new ErrorCode(23018, "您已经点赞");
    ErrorCode HABIT_ILLEGAL_DISTANCE = new ErrorCode(23019, "跑量过大，请检查重新输入～");
    ErrorCode HABIT_CLOCK_NOT_EXIST = new ErrorCode(23020, "打卡不存在");

    // 调查表单
    ErrorCode SURVEY_CODE_EXIST = new ErrorCode(24000, "调查文件CODE存在");
    ErrorCode SURVEY_FINISH = new ErrorCode(24001, "调查已经完成");
    ErrorCode SURVEY_NOT_EXIST = new ErrorCode(24002, "调查不存在");

    // 微信相关
    ErrorCode WX_CODE_FAILURE = new ErrorCode(25000, "微信code过期失效");

    // 跑团
    ErrorCode CLUB_NOT_EXISTS = new ErrorCode(26000, "跑团不存在", "The running group does not exist");
    ErrorCode CLUB_PERMISSION_DENIED = new ErrorCode(26001, "用户管理权限不足");
    ErrorCode CLUB_NAME_REPEAT = new ErrorCode(26002, "跑团名称已存在");
    ErrorCode CLUB_MEMBER_NOT_EXISTS = new ErrorCode(26003, "跑团成员不存在");
    ErrorCode CLUB_APPLY_NOT_EXIST = new ErrorCode(26004, "跑团加入申请记录不存在");
    ErrorCode CLUB_APPLY_HAVE_REVIEW = new ErrorCode(26005, "跑团加入申请记录已经审核完成");
    ErrorCode CLUB_ADJUST_GROUP_MEMBER_EXIST = new ErrorCode(26006, "相关成员已在跑团内，不可重复添加~");
    ErrorCode CLUB_MEMBER_IS_ADMIN = new ErrorCode(26007, "用户是跑团管理员");
    ErrorCode CLUB_APPLY_EXIST = new ErrorCode(26008, "跑团加入申请记录已提交，请等待跑团管理员审核");
    ErrorCode CLUB_RING_NOT_EXIST = new ErrorCode(26009, "跑团擂台不存在");
    ErrorCode CLUB_ADDRESS_ERROR = new ErrorCode(26010, "创建跑团地址不能为空");
    ErrorCode CLUB_ACTIVITY_SUBMIT_ERROR = new ErrorCode(26011, "跑团活动提交报名错误");
    ErrorCode CLUB_ACTIVITY_UPSERT_ERROR = new ErrorCode(26012, "跑团活动时间错误");
    ErrorCode CLUB_ACTIVITY_SUBMIT_SIGN_ERROR = new ErrorCode(26013, "提交报名错误，请稍后再试");
    ErrorCode CLUB_ACTIVITY_CANCEL_SIGN_ERROR = new ErrorCode(26014, "取消报名错误，请稍后再试");
    ErrorCode CLUB_BATCH_APPOINT_ADMIN_ERROR = new ErrorCode(26015, "批量任命管理员错误");
    ErrorCode CLUB_TAG_ERROR = new ErrorCode(26016, "跑团标签错误");

    // 荣誉证书
    ErrorCode HONOR_CERT_NOT_EXISTS = new ErrorCode(27000, "荣誉证书不存在");
    ErrorCode HONOR_CERT_NOT_BELONG_USER = new ErrorCode(27001, "荣誉证书不属于用户");
    ErrorCode HONOR_CERT_CANNOT_CREATE = new ErrorCode(27002, "目前只能创建3个荣誉证书哦");

    // 聊天
    ErrorCode USER_CHAT_MESSAGE_EMPTY = new ErrorCode(28000, "用户聊天消息不能为空");
    ErrorCode USER_CHAT_NOT_EXISTS = new ErrorCode(28001, "用户聊天不存在");
    ErrorCode USER_CHAT_NOT_BELONG_USER = new ErrorCode(28002, "用户聊天不属于当前用户");
    ErrorCode USER_CHAT_MESSAGE_SEND_SELF = new ErrorCode(28003, "不能给自己发送聊天消息");
    ErrorCode USER_CHAT_DELETED = new ErrorCode(28004, "用户聊天已删除");
    ErrorCode USER_CHAT_MESSAGE_ILLEGAL = new ErrorCode(28005, "聊天内容包含敏感词");

    // 跑步知识
    ErrorCode KNOWLEDGE_VIDEO_CATEGORY_NOT_EXIST = new ErrorCode(29000, "视频类目不存在");
    ErrorCode KNOWLEDGE_VIDEO_NOT_EXIST = new ErrorCode(29001, "视频不存在");
    ErrorCode KNOWLEDGE_ARTICLE_NOT_EXIST = new ErrorCode(29002, "文章不存在");

    // 跑步记录
    ErrorCode USER_RUN_NOT_EXISTS = new ErrorCode(30000, "跑步记录不存在");
    ErrorCode USER_PERMISSION_DENIED_VIEW_RUN = new ErrorCode(30001, "用户查看跑步记录权限不足");

    // 在线参数
    ErrorCode ONLINE_CONFIG_NOT_EXISTS = new ErrorCode(31000, "在线参数不存在");

    // 商品
    ErrorCode SHOP_GOODS_NOT_EXISTS = new ErrorCode(32000, "该商品不存在");
    ErrorCode SHOP_RECOMMEND_GOODS_NOT_EXISTS = new ErrorCode(32001, "该推荐商品不存在");
    ErrorCode SHOP_GOODS_MARKET_LT_SALE = new ErrorCode(32003, "该商品的市场价小于销售价");
    ErrorCode SHOP_RELATION_CONTAINS_GOOD = new ErrorCode(32004, "该商品已包含在推荐中");
    ErrorCode SHOP_GOODS_SKU_LIST_EMPTY = new ErrorCode(32005, "商品SKU列表为空");
    ErrorCode SHOP_GOODS_RELATION_NOT_EMPTY = new ErrorCode(32006, "商品关联不为空");
    ErrorCode SHOP_GOODS_CREATE_ERROR = new ErrorCode(32007, "商品创建失败");
    ErrorCode SHOP_GOODS_RELATION_ERROR = new ErrorCode(32008, "商品关联错误");

    // 新线上赛
    ErrorCode ONLINE_RACE_NOT_EXISTS = new ErrorCode(33000, "线上赛不存在");
    ErrorCode ONLINE_PROJECT_NOT_EXISTS = new ErrorCode(33001, "项目不存在");
    ErrorCode ONLINE_SIGN_PACKAGE_NOT_EXISTS = new ErrorCode(33002, "报名套餐并不存在");
    ErrorCode ONLINE_SIGN_PACKAGE_CONTAINS_GOOD = new ErrorCode(33003, "该商品已包含在报名套餐中");
    ErrorCode ONLINE_RECOMMEND_GOODS_NOT_EXISTS = new ErrorCode(33004, "该线上赛推荐商品并不存在");
    ErrorCode ONLINE_USER_SIGN_EXISTS = new ErrorCode(33005, "线上赛用户报名信息已存在");
    ErrorCode ONLINE_RACE_SIGN_END = new ErrorCode(33006, "线上赛报名已结束");
    ErrorCode ONLINE_USER_SIGN_SUBMIT_ERROR = new ErrorCode(33007, "线上赛提交报名错误");
    ErrorCode ONLINE_PROJECT_CONTAINS_USER = new ErrorCode(33008, "该线上赛项目已有用户报名");
    ErrorCode ONLINE_USER_SIGN_NOT_BELONG_USER = new ErrorCode(33009, "该线上赛报名信息不属于当前用户");
    ErrorCode ONLINE_ONLY_CLUB_JOIN = new ErrorCode(33010, "该线上赛仅限团报");
    ErrorCode ONLINE_RACE_SIGN_NOT_START = new ErrorCode(33011, "线上赛报名未开始");
    ErrorCode ONLINE_PROJECT_SETTINGS_ERROR = new ErrorCode(33012, "线上赛项目设置错误");
    ErrorCode ONLINE_USER_SIGN_NOT_FREE = new ErrorCode(33013, "线上赛报名不是免费的");
    ErrorCode ONLINE_RACE_ALREADY_END = new ErrorCode(33014, "线上赛已结束");
    ErrorCode ONLINE_SIGN_PACKAGE_ALREADY_SIGN = new ErrorCode(33015, "线上赛套餐已存在报名信息");
    ErrorCode ONLINE_SIGN_ERROR = new ErrorCode(33016, "提交报名失败");
    ErrorCode ONLINE_USER_SIGN_REFUND_ERROR = new ErrorCode(33017, "用户报名退款失败");
    ErrorCode ONLINE_SERIES_NOT_EXISTS = new ErrorCode(33018, "系列不存在");
    ErrorCode ONLINE_PROJECT_POINT_NOT_EXISTS = new ErrorCode(33019, "项目点位不存在");
    ErrorCode ONLINE_CLOCK_ERROR = new ErrorCode(33020, "打卡失败");
    ErrorCode ONLINE_REVIEW_ERROR = new ErrorCode(33021, "审核错误");
    ErrorCode ONLINE_RACE_UPDATE_ERROR = new ErrorCode(33022, "线上赛更新失败");
    ErrorCode ONLINE_RACE_CREATE_ERROR = new ErrorCode(33023, "线上赛创建失败");

    // 收货地址
    ErrorCode USER_SHIPPING_ADDRESS_NOT_EXISTS = new ErrorCode(34000, "该用户地址并不存在");
    ErrorCode USER_SHIPPING_ADDRESS_MESSAGE_ERROR = new ErrorCode(34000, "请检查是否正确填写地址信息");

    // 短信
    ErrorCode SMS_SEND_LIMITED = new ErrorCode(35000, "短信发送受限");
    ErrorCode SMS_MOBILE_ILLEGAL = new ErrorCode(35001, "请正确填写手机");
    ErrorCode SMS_SEND_FREQUENTLY = new ErrorCode(35002, "短信发送频繁");

    // 用户登录
    ErrorCode USER_LOGIN_CODE_ERROR = new ErrorCode(36000, "验证码错误");

    // 快递物流查询
    ErrorCode SHIP_GET_INFO_ERROR = new ErrorCode(37001, "快递物流查询失败");

    // 日志捞回
    ErrorCode LOG_COLLECT_RECORD_EXISTS = new ErrorCode(38000, "日志捞回记录已存在");

    // 阳光18
    ErrorCode SUNSHINE_GUARDIAN_CAN_NOT_JOIN_BY_AGE = new ErrorCode(39000, "监护人应大于18岁");
    ErrorCode SUNSHINE_USER_CAN_NOT_JOIN_BY_AGE = new ErrorCode(39001, "报名人年龄应该小于18岁");

    // 线下赛
    ErrorCode OFFLINE_RACE_NOT_EXISTS = new ErrorCode(40000, "线下赛不存在");
    ErrorCode OFFLINE_CHANNEL_NOT_EXISTS = new ErrorCode(40001, "线下赛通道不存在");
    ErrorCode OFFLINE_PROJECT_NOT_EXISTS = new ErrorCode(40002, "线下赛项目不存在");
    ErrorCode OFFLINE_SIGN_TIME_OUTSIDE = new ErrorCode(40003, "不在可报名时间段内", "Not within the registration period");
    ErrorCode OFFLINE_INVITE_CODE_INVALID = new ErrorCode(40004, "线下赛邀请码无效", "Invalid invitation code");
    ErrorCode OFFLINE_RACE_NOT_PUBLISH = new ErrorCode(40005, "线下赛未开放", "The event is not open");
    ErrorCode OFFLINE_CHANNEL_NOT_PUBLISH = new ErrorCode(40006, "线下赛通道未开放", "Channel is not open");
    ErrorCode OFFLINE_AGE_ILLEGAL = new ErrorCode(40007, "年龄不符合参赛要求");
    ErrorCode OFFLINE_USER_SIGN_NOT_EXISTS = new ErrorCode(40008, "用户报名信息不存在");
    ErrorCode OFFLINE_USER_SIGN_QUIT_ERROR = new ErrorCode(40009, "退赛失败");
    ErrorCode OFFLINE_QUIT_INFO_INCOMPLETE = new ErrorCode(40010, "退款信息不完整");
    ErrorCode OFFLINE_ADDITION_INCOMPLETE = new ErrorCode(40011, "资质证明不完整");
    ErrorCode OFFLINE_GOODS_NOT_PUBLISH = new ErrorCode(40012, "您选购的商品已下架，请重新提交报名");
    ErrorCode OFFLINE_USER_ALREADY_SIGN = new ErrorCode(40013, "该证件号码已存在报名信息", "The certificate number already exists in the registration information");
    ErrorCode OFFLINE_PUBLIC_SIGN_CANT_USE_CODE = new ErrorCode(40014, "公益报名不能使用邀请码", "Invitation codes cannot be used for charity registration");
    ErrorCode OFFLINE_CANT_UPDATE_ADDITION = new ErrorCode(40015, "当前用户不用更新资质证明");
    ErrorCode OFFLINE_CANT_CHOOSE_BIBNO = new ErrorCode(40016, "当前用户不能自选号码");
    ErrorCode OFFLINE_CHOOSE_BIBNO_ILLEGAL = new ErrorCode(40017, "自选号码不合法");
    ErrorCode OFFLINE_BIBNO_EXISTS = new ErrorCode(40018, "号码已存在");
    ErrorCode OFFLINE_USER_SIGN_NOT_BELONG_USER = new ErrorCode(40019, "该报名信息不属于当前用户");
    ErrorCode OFFLINE_USER_SIGN_PAID = new ErrorCode(40020, "用户报名已支付");
    ErrorCode OFFLINE_BIBNO_USED = new ErrorCode(40021, "号码已使用");
    ErrorCode OFFLINE_BIBNO_DUPLICATE = new ErrorCode(40022, "号码重复");
    ErrorCode OFFLINE_BIBNO_NOT_EXISTS = new ErrorCode(40023, "号码不存在");
    ErrorCode OFFLINE_USER_BIBNO_EXISTS = new ErrorCode(40024, "已有号码");
    ErrorCode OFFLINE_CHANNEL_HAVE_PROJECT = new ErrorCode(40025, "当前通道已有项目");
    ErrorCode OFFLINE_PROJECT_HAVE_SIGN = new ErrorCode(40026, "当前项目已有报名");
    ErrorCode OFFLINE_SHIP_INFO_NOT_FULL = new ErrorCode(40027, "收货信息不完整");
    ErrorCode OFFLINE_DOCUMENT_NOT_ENABLE = new ErrorCode(40028, "文档未启用");
    ErrorCode OFFLINE_INVITE_CODE_CREATE_ERROR = new ErrorCode(40029, "邀请码创建失败");
    ErrorCode OFFLINE_IMPORT_SIGN_ERROR = new ErrorCode(40030, "导入报名名单失败");
    ErrorCode OFFLINE_USER_BIBNO_UPDATE_ERROR = new ErrorCode(40031, "用户号码更新失败");
    ErrorCode OFFLINE_USER_SIGN_NOT_PAY = new ErrorCode(40032, "用户报名未支付");
    ErrorCode OFFLINE_USER_SIGN_NOT_REVIEW_PASS = new ErrorCode(40033, "用户报名审核未通过", "User registration review failed");
    ErrorCode OFFLINE_USER_SIGN_NOT_BALLOT = new ErrorCode(40034, "用户报名未中签", "User registration failed");
    ErrorCode OFFLINE_USER_SIGN_ALREADY_CANCEL = new ErrorCode(40035, "用户报名已取消", "User registration has been cancelled");
    ErrorCode OFFLINE_PROJECT_BIBNO_SET_ERROR = new ErrorCode(40036, "项目号码设置错误");
    ErrorCode OFFLINE_USER_SIGN_NOT_SUCCESS = new ErrorCode(40037, "用户报名未成功");
    ErrorCode OFFLINE_PROJECT_NOT_SET_BIBNO_RULE = new ErrorCode(40038, "项目未设置号码规则");
    ErrorCode OFFLINE_BIBNO_RULE_SUB_EXIST = new ErrorCode(40039, "当前前缀已被其他号码规则使用");
    ErrorCode OFFLINE_BIBNO_RULE_GENERATING = new ErrorCode(40040, "号码生成中，请勿重复生成或修改规则");
    ErrorCode OFFLINE_USER_RESULT_IMPORTING = new ErrorCode(40041, "成绩导入中，请等待任务完成");
    ErrorCode OFFLINE_INVITE_CODE_CREATING = new ErrorCode(40042, "邀请码创建中，请等待任务完成");
    ErrorCode OFFLINE_CERT_TEMPLATE_NOT_EXISTS = new ErrorCode(40043, "证书未配置");
    ErrorCode OFFLINE_BIBNO_TEMPLATE_NOT_EXISTS = new ErrorCode(40044, "号码布未配置");
    ErrorCode OFFLINE_BIBNO_RULE_SETTING_ERROR = new ErrorCode(40045, "号码规则设置有误");
    ErrorCode OFFLINE_USER_BIBNO_IMPORTING = new ErrorCode(40046, "用户号码导入中，请等待任务完成");
    ErrorCode OFFLINE_BIBNO_ASSIGNING = new ErrorCode(40047, "号码分配中，请等待任务完成");
    ErrorCode OFFLINE_USER_SIGN_BALLOT_IMPORTING = new ErrorCode(40048, "中签名单导入中，请等待任务完成");
    ErrorCode OFFLINE_BIBNO_RULE_NOT_BIND_PROJECT = new ErrorCode(40049, "号码规则未绑定任何项目");
    ErrorCode OFFLINE_USER_SIGN_FULL = new ErrorCode(40050, "项目报名人数已满", "The project enrollment is full");
    ErrorCode OFFLINE_CHANNEL_NEED_CODE = new ErrorCode(40051, "该通道仅限邀请码报名", "This channel is only open for invitation code registration");
    ErrorCode OFFLINE_USER_IN_BLACKLIST = new ErrorCode(40052, "用户不符合参赛资格", "User is not eligible to participate");
    ErrorCode OFFLINE_CHANNEL_UPDATE_ERROR = new ErrorCode(40053, "通道更新失败");
    ErrorCode OFFLINE_PROJECT_UPDATE_ERROR = new ErrorCode(40054, "项目更新失败");
    ErrorCode OFFLINE_USER_SIGN_UPDATE_ERROR = new ErrorCode(40055, "报名信息修改失败");
    ErrorCode OFFLINE_USER_SIGN_SCHOOL_EMPTY = new ErrorCode(40056, "高校通道学校不能为空", "The university channel school cannot be empty");
    ErrorCode OFFLINE_TEAM_NAME_EMPTY = new ErrorCode(40057, "队伍名称不能为空");
    ErrorCode OFFLINE_TEAM_NAME_DUPLICATE = new ErrorCode(40058, "队伍名称已有人使用");
    ErrorCode OFFLINE_TEAM_NOT_FOUND = new ErrorCode(40059, "队伍不存在或已解散");
    ErrorCode OFFLINE_TEAM_MEMBER_LIMIT = new ErrorCode(40060, "队伍人数已满");
    ErrorCode OFFLINE_TEAM_CHANNEL_NOT_MATCH = new ErrorCode(40061, "不能跨通道组队");
    ErrorCode OFFLINE_TEAM_PROJECT_NOT_MATCH = new ErrorCode(40062, "不能跨项目组队");
    ErrorCode OFFLINE_TEAM_USER_ALREADY_JOIN = new ErrorCode(40063, "用户已有队伍，请先退出当前队伍");
    ErrorCode OFFLINE_TEAM_JOIN_ERROR = new ErrorCode(40064, "加入队伍失败");
    ErrorCode OFFLINE_TEAM_MODIFY_END = new ErrorCode(40065, "队伍修改已截止");
    ErrorCode OFFLINE_TEAM_NOT_CAPTAIN = new ErrorCode(40065, "当前用户不是队长");
    ErrorCode OFFLINE_TEAM_USER_NOT_IN_TEAM = new ErrorCode(40066, "该用户不在当前队伍内");
    ErrorCode OFFLINE_TEAM_OPEN_ERROR = new ErrorCode(40067, "开启组队失败");
    ErrorCode OFFLINE_TEAM_CREATE_ERROR = new ErrorCode(40068, "创建队伍失败");
    ErrorCode OFFLINE_TEAM_USER_IS_CAPTAIN = new ErrorCode(40069, "当前用户是队长");
    ErrorCode OFFLINE_USER_SIGN_NOT_SUPPORT_CLUB = new ErrorCode(40070, "请选择支持跑团", "Please choose to support the running group");
    ErrorCode OFFLINE_USER_SIGN_ENTER_ERROR = new ErrorCode(40071, "报名入口错误", "Registration entry error");
    ErrorCode OFFLINE_USER_SIGN_CHANGE_PROJECT_ERROR = new ErrorCode(40072, "当前用户报名更改项目失败");
    ErrorCode OFFLINE_USER_SIGN_TRANSFER_ERROR = new ErrorCode(40073, "当前用户报名转让名额失败");
    ErrorCode OFFLINE_USER_SIGN_REFUND_ERROR = new ErrorCode(40074, "用户报名退款失败");
    ErrorCode OFFLINE_TIMING_CREATE_ERROR = new ErrorCode(40075, "计时创建失败");
    ErrorCode OFFLINE_TIMING_NOT_EXISTS = new ErrorCode(40076, "计时不存在");
    ErrorCode OFFLINE_INVITE_CODE_BATCH_INVALIDING = new ErrorCode(40077, "邀请码作废中，请等待任务完成");
    ErrorCode OFFLINE_USER_SIGN_ALREADY_REVIEW = new ErrorCode(40078, "用户报名不是待审核");
    ErrorCode OFFLINE_RACE_TAG_ERROR = new ErrorCode(40079, "线下赛标签错误");
    ErrorCode OFFLINE_BIBNO_SELECT_NUMBER_ERROR = new ErrorCode(40080, "选号失败");
    ErrorCode OFFLINE_SELECT_NUMBER_RECORD_NOT_EXISTS = new ErrorCode(40081, "选号记录不存在");
    ErrorCode OFFLINE_BIBNO_RULE_NOT_EXISTS = new ErrorCode(40082, "号码规则不存在");
    ErrorCode OFFLINE_SELECT_NUMBER_RECORD_ALREADY_PAID = new ErrorCode(40083, "选号记录已支付");
    ErrorCode OFFLINE_RACE_ERROR = new ErrorCode(40084, "线下赛错误");
    ErrorCode OFFLINE_GOODS_UPDATE_ERROR = new ErrorCode(40085, "商品更新失败，请稍后再试");
    ErrorCode OFFLINE_PROJECT_ERROR = new ErrorCode(40086, "线下赛项目错误");
    ErrorCode OFFLINE_BIBNO_ALREADY_RELEASE = new ErrorCode(40087, "号码已释放");
    ErrorCode OFFLINE_BIBNO_ALREADY_SELECT = new ErrorCode(40088, "号码已被选");
    ErrorCode OFFLINE_USER_SIGN_ERROR = new ErrorCode(40089, "报名错误");
    ErrorCode OFFLINE_USER_RESULT_THIRD_UPLOAD_ERROR = new ErrorCode(40090, "第三方平台上传成绩失败");
    ErrorCode OFFLINE_USER_RESULT_THIRD_QUERY_ERROR = new ErrorCode(40091, "第三方平台查询成绩失败");


    // 验证码
    ErrorCode VERIFY_CODE_ERROR = new ErrorCode(41000, "验证码错误");

    // 租户
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode(42000, "租户不存在");
    ErrorCode TENANT_DISABLE = new ErrorCode(42001, "租户已禁用");
    ErrorCode TENANT_FORBIDDEN = new ErrorCode(42002, "禁止访问");

    // 社区
    ErrorCode COMMUNITY_TOPIC_NOT_EXISTS = new ErrorCode(43000, "话题不存在");
    ErrorCode COMMUNITY_TAG_NOT_EXIST = new ErrorCode(43001, "标签不存在");
    ErrorCode COMMUNITY_POST_NOT_EXISTS = new ErrorCode(43002, "动态不存在");
    ErrorCode COMMUNITY_COMMENT_NOT_EXISTS = new ErrorCode(43003, "评论不存在");
    ErrorCode COMMUNITY_PERMISSION_DENIED = new ErrorCode(43004, "权限不足");
    ErrorCode COMMUNITY_POST_ALREADY_DELETE = new ErrorCode(43005, "动态已删除");
    ErrorCode COMMUNITY_POST_ALREADY_RECOVER = new ErrorCode(43006, "动态已恢复");
    ErrorCode COMMUNITY_COMMENT_ALREADY_DELETE = new ErrorCode(43007, "评论已删除");
    ErrorCode COMMUNITY_COMMENT_ALREADY_RECOVER = new ErrorCode(43008, "评论已恢复");
    ErrorCode COMMUNITY_POST_NOT_BELONG_USER = new ErrorCode(43009, "该不属于当前用户");
    ErrorCode COMMUNITY_OFFICIAL_POST_CANT_DELETE = new ErrorCode(43010, "官方动态不支持删除");
    ErrorCode COMMUNITY_CONTENT_SENSITIVE = new ErrorCode(43011, "内容存在敏感词");
    ErrorCode COMMUNITY_RACE_ALREADY_BIND_TOPIC = new ErrorCode(43012, "赛事已绑定话题");
    ErrorCode COMMUNITY_RACE_NOT_PUBLISH = new ErrorCode(43013, "赛事未发布");
    ErrorCode COMMUNITY_TOPIC_NOT_SIGN = new ErrorCode(43014, "用户未报名");
    ErrorCode COMMUNITY_TOPIC_UPSERT_ERROR = new ErrorCode(43015, "创建/更新话题失败");
    ErrorCode COMMUNITY_TAG_UPSERT_ERROR = new ErrorCode(43016, "创建/更新标签失败");
    ErrorCode COMMUNITY_CREATE_COMMENT_ERROR = new ErrorCode(43017, "创建评论失败，请稍后再试");
    ErrorCode COMMUNITY_DELETE_COMMENT_ERROR = new ErrorCode(43018, "删除评论失败，请稍后再试");
    ErrorCode COMMUNITY_ADD_LIKE_ERROR = new ErrorCode(43019, "添加点赞失败，请稍后再试");
    ErrorCode COMMUNITY_CANCEL_LIKE_ERROR = new ErrorCode(43020, "取消点赞失败，请稍后再试");
    ErrorCode COMMUNITY_USER_BLOCKED = new ErrorCode(43021, "很抱歉，您已被禁言，暂时无法发表评动态与评论。可通过消息中心-系统消息查看禁言详情，如有异议，请联系客服");
    ErrorCode COMMUNITY_ADMIN_CREATE_ERROR = new ErrorCode(43022, "创建管理员失败");
    ErrorCode COMMUNITY_ADMIN_NOT_EXISTS = new ErrorCode(43023, "管理员不存在");
    ErrorCode COMMUNITY_POST_ALREADY_LIMIT = new ErrorCode(43024, "动态已限流");
    ErrorCode COMMUNITY_COMMENT_ALREADY_LIMIT = new ErrorCode(43025, "评论已限流");
    ErrorCode COMMUNITY_OFFICIAL_POST_CANT_LIMIT = new ErrorCode(43026, "官方动态不支持限流");
    ErrorCode COMMUNITY_POST_ALREADY_ILLEGAL = new ErrorCode(43027, "动态不存在");

    // 敏感词
    ErrorCode SENSITIVE_WORD_ALREADY_EXIST = new ErrorCode(44000, "敏感词已存在");

    // 退款
    ErrorCode REFUND_ERROR = new ErrorCode(45000, "退款失败");
    ErrorCode ORDER_REFUND_RECORD_NOT_EXISTS = new ErrorCode(45001, "退款记录不存在");

    // 反馈
    ErrorCode APP_FEEDBACK_NOT_EXISTS = new ErrorCode(46000, "反馈不存在");
    ErrorCode APP_FEEDBACK_PERMISSION_DENIED = new ErrorCode(46001, "反馈权限不足");

    // 相册管理
    ErrorCode PHOTO_ALBUM_NOT_EXISTS = new ErrorCode(47000, "相册不存在");
    ErrorCode PHOTO_ALBUM_DISABLED = new ErrorCode(47001, "相册已禁用");
    ErrorCode PHOTO_ALBUM_DISCOUNT_NOT_EXISTS = new ErrorCode(47002, "相册折扣配置不存在");
    ErrorCode PHOTO_ALBUM_SHARE_RATIO_ERROR = new ErrorCode(47003, "摄影师分成比例和公司分成比例之和必须等于100%");
    ErrorCode PHOTO_ALBUM_PRICE_ERROR = new ErrorCode(47004, "照片价格必须大于0");
    ErrorCode PHOTO_ALBUM_DISCOUNT_CONFIG_ERROR = new ErrorCode(47005, "折扣配置错误");
    ErrorCode APP_FEEDBACK_RESOLVED = new ErrorCode(46002, "反馈已解决");

    // 定位共享
    ErrorCode LOCATION_SHARE_ROOM_NOT_EXISTS = new ErrorCode(47000, "房间不存在");
    ErrorCode LOCATION_SHARE_MEMBER_NOT_EXISTS = new ErrorCode(47001, "成员不存在");
    ErrorCode LOCATION_SHARE_ROOM_NOT_BELONG_USER = new ErrorCode(47002, "您不是该房间创建者");
    ErrorCode LOCATION_SHARE_ROOM_VERIFY_CODE_ERROR = new ErrorCode(47003, "验证码错误");
    ErrorCode LOCATION_SHARE_ROOM_ALREADY_END = new ErrorCode(47004, "房间已结束");
    ErrorCode LOCATION_SHARE_ROOM_CREATE_ERROR = new ErrorCode(47005, "创建房间失败，请稍后重新尝试");
    ErrorCode LOCATION_SHARE_ROOM_SHARE_LIMIT = new ErrorCode(47006, "共享定位人数已达上限");
    ErrorCode LOCATION_SHARE_ROOM_MEMBER_LIMIT = new ErrorCode(47007, "房间人数已达上限");
    ErrorCode LOCATION_SHARE_ROOM_OWNER_CAN_NOT_QUIT = new ErrorCode(47008, "房主不能退出房间");
    ErrorCode LOCATION_SHARE_ROOM_ALREADY_DISSOLVE = new ErrorCode(47009, "房间已解散");

    // 计时
    ErrorCode TIGER_TIMING_GET_RACE_SETTING_ERROR = new ErrorCode(48000, "赛虎计时获取赛事设置失败");
    ErrorCode TIGER_TIMING_GET_ATHLETE_LIST_ERROR = new ErrorCode(48000, "赛虎计时获取运动员列表失败");
    ErrorCode TIGER_TIMING_GET_PART_TIME_RESULT_LIST_ERROR = new ErrorCode(48000, "赛虎计时获取分段成绩列表失败");

    // 标签
    ErrorCode TAG_GROUP_NOT_EXISTS = new ErrorCode(49000, "标签分组不存在");
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(49000, "标签不存在");

    // APP勋章
    ErrorCode APP_MEDAL_GET_DETAIL_ERROR = new ErrorCode(50000, "获取勋章详情失败");

    // 企业微信
    ErrorCode WX_CP_GET_USER_INFO_ERROR = new ErrorCode(51000, "获取企业微信用户信息失败");

    // APP推送
    ErrorCode APP_PUSH_ERROR = new ErrorCode(52000, "APP推送错误");

    // APP路线
    ErrorCode APP_ROUTE_APPLY_UPDATE_ERROR = new ErrorCode(53000, "APP路线申请更新失败");

    // 谱时
    ErrorCode PHOTO_PLUS_ERROR = new ErrorCode(54000, "谱时错误");

}
