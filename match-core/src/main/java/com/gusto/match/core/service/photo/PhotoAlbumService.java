package com.gusto.match.core.service.photo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.framework.core.exception.ServiceException;
import com.gusto.match.core.dao.photo.PhotoAlbumDao;
import com.gusto.match.core.exception.MatchErrorCode;
import com.gusto.match.model.entity.photo.PhotoAlbum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.gusto.framework.core.util.ExceptionUtils.throwIf;

/**
 * 相册管理服务
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Service
public class PhotoAlbumService extends ServiceImpl<PhotoAlbumDao, PhotoAlbum> {

    @Autowired
    private PhotoAlbumDiscountService photoAlbumDiscountService;

    /**
     * 创建相册
     *
     * @param photoAlbum 相册信息
     * @return 相册ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createAlbum(PhotoAlbum photoAlbum) {
        // 验证分成比例
        throwIf(!photoAlbum.isShareRatioValid(), MatchErrorCode.PHOTO_ALBUM_SHARE_RATIO_ERROR);

        // 验证线下赛ID是否存在
        throwIf(photoAlbum.getOfflineRaceId() == null, MatchErrorCode.PARAM_ERROR, "线下赛ID不能为空");

        // 验证谱时相册ID列表
        throwIf(CollUtil.isEmpty(photoAlbum.getPhotoPlusAlbumIds()), MatchErrorCode.PARAM_ERROR, "谱时相册ID列表不能为空");

        // 验证单张照片价格
        throwIf(photoAlbum.getSinglePhotoPrice() == null || photoAlbum.getSinglePhotoPrice().compareTo(java.math.BigDecimal.ZERO) <= 0,
                MatchErrorCode.PHOTO_ALBUM_PRICE_ERROR);

        // 设置默认值
        if (photoAlbum.getStatus() == null) {
            photoAlbum.setStatus(1); // 默认启用
        }
        if (photoAlbum.getSortWeight() == null) {
            photoAlbum.setSortWeight(0);
        }

        save(photoAlbum);
        return photoAlbum.getAlbumId();
    }

    /**
     * 更新相册
     *
     * @param photoAlbum 相册信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAlbum(PhotoAlbum photoAlbum) {
        throwIf(photoAlbum.getAlbumId() == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        PhotoAlbum existingAlbum = getById(photoAlbum.getAlbumId());
        throwIf(existingAlbum == null, MatchErrorCode.PHOTO_ALBUM_NOT_EXISTS);

        // 验证分成比例
        if (photoAlbum.getPhotographerShareRatio() != null && photoAlbum.getCompanyShareRatio() != null) {
            throwIf(!photoAlbum.isShareRatioValid(), MatchErrorCode.PHOTO_ALBUM_SHARE_RATIO_ERROR);
        }

        // 验证单张照片价格
        if (photoAlbum.getSinglePhotoPrice() != null) {
            throwIf(photoAlbum.getSinglePhotoPrice().compareTo(java.math.BigDecimal.ZERO) <= 0,
                    MatchErrorCode.PHOTO_ALBUM_PRICE_ERROR);
        }

        updateById(photoAlbum);
    }

    /**
     * 删除相册
     *
     * @param albumId 相册ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAlbum(Long albumId) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        PhotoAlbum existingAlbum = getById(albumId);
        throwIf(existingAlbum == null, MatchErrorCode.PHOTO_ALBUM_NOT_EXISTS);

        // 删除相册折扣配置
        photoAlbumDiscountService.deleteByAlbumId(albumId);
        
        // 删除相册
        removeById(albumId);
    }

    /**
     * 根据ID获取相册详情
     *
     * @param albumId 相册ID
     * @return 相册详情
     */
    public PhotoAlbum getAlbumById(Long albumId) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        PhotoAlbum album = getById(albumId);
        throwIf(album == null, MatchErrorCode.PHOTO_ALBUM_NOT_EXISTS);
        
        return album;
    }

    /**
     * 根据线下赛ID获取相册列表
     *
     * @param offlineRaceId 线下赛ID
     * @return 相册列表
     */
    public List<PhotoAlbum> getAlbumsByOfflineRaceId(Long offlineRaceId) {
        throwIf(offlineRaceId == null, MatchErrorCode.PARAM_ERROR, "线下赛ID不能为空");
        
        return baseMapper.selectByOfflineRaceId(offlineRaceId);
    }

    /**
     * 根据线下赛ID获取启用的相册列表
     *
     * @param offlineRaceId 线下赛ID
     * @return 启用的相册列表
     */
    public List<PhotoAlbum> getEnabledAlbumsByOfflineRaceId(Long offlineRaceId) {
        throwIf(offlineRaceId == null, MatchErrorCode.PARAM_ERROR, "线下赛ID不能为空");
        
        return baseMapper.selectByOfflineRaceIdAndStatus(offlineRaceId, 1);
    }

    /**
     * 根据谱时相册ID获取相册
     *
     * @param photoPlusAlbumId 谱时相册ID
     * @return 相册信息
     */
    public PhotoAlbum getAlbumByPhotoPlusAlbumId(String photoPlusAlbumId) {
        throwIf(StrUtil.isBlank(photoPlusAlbumId), MatchErrorCode.PARAM_ERROR, "谱时相册ID不能为空");
        
        return baseMapper.selectByPhotoPlusAlbumId(photoPlusAlbumId);
    }

    /**
     * 分页查询相册列表
     *
     * @param current 当前页
     * @param size 页大小
     * @param offlineRaceId 线下赛ID（可选）
     * @param status 状态（可选）
     * @param albumName 相册名称（可选）
     * @return 分页结果
     */
    public IPage<PhotoAlbum> getAlbumPage(Long current, Long size, Long offlineRaceId, Integer status, String albumName) {
        LambdaQueryWrapper<PhotoAlbum> wrapper = Wrappers.lambdaQueryWrapper();
        
        if (offlineRaceId != null) {
            wrapper.eq(PhotoAlbum::getOfflineRaceId, offlineRaceId);
        }
        if (status != null) {
            wrapper.eq(PhotoAlbum::getStatus, status);
        }
        if (StrUtil.isNotBlank(albumName)) {
            wrapper.like(PhotoAlbum::getAlbumName, albumName);
        }
        
        wrapper.orderByDesc(PhotoAlbum::getSortWeight, PhotoAlbum::getAlbumId);
        
        return page(new Page<>(current, size), wrapper);
    }

    /**
     * 启用/禁用相册
     *
     * @param albumId 相册ID
     * @param status 状态：0-禁用 1-启用
     */
    public void updateAlbumStatus(Long albumId, Integer status) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        throwIf(status == null || (status != 0 && status != 1), MatchErrorCode.PARAM_ERROR, "状态参数错误");
        
        PhotoAlbum existingAlbum = getById(albumId);
        throwIf(existingAlbum == null, MatchErrorCode.PHOTO_ALBUM_NOT_EXISTS);

        PhotoAlbum updateAlbum = new PhotoAlbum();
        updateAlbum.setAlbumId(albumId);
        updateAlbum.setStatus(status);
        updateById(updateAlbum);
    }
}
