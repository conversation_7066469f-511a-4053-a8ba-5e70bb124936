package com.gusto.match.core.service.photo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.framework.core.exception.ServiceException;
import com.gusto.match.core.dao.photo.PhotoAlbumDiscountDao;
import com.gusto.match.core.exception.MatchErrorCode;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

import static com.gusto.framework.core.util.ExceptionUtils.throwIf;

/**
 * 相册折扣配置服务
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Service
public class PhotoAlbumDiscountService extends ServiceImpl<PhotoAlbumDiscountDao, PhotoAlbumDiscount> {

    /**
     * 批量创建折扣配置
     *
     * @param albumId 相册ID
     * @param discounts 折扣配置列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void createDiscounts(Long albumId, List<PhotoAlbumDiscount> discounts) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        throwIf(CollUtil.isEmpty(discounts), MatchErrorCode.PARAM_ERROR, "折扣配置列表不能为空");

        // 先删除原有配置
        deleteByAlbumId(albumId);

        // 验证并设置折扣配置
        for (int i = 0; i < discounts.size(); i++) {
            PhotoAlbumDiscount discount = discounts.get(i);
            validateDiscount(discount);
            
            discount.setAlbumId(albumId);
            if (discount.getStatus() == null) {
                discount.setStatus(1); // 默认启用
            }
            if (discount.getSortOrder() == null) {
                discount.setSortOrder(i + 1);
            }
        }

        // 批量保存
        saveBatch(discounts);
    }

    /**
     * 更新折扣配置
     *
     * @param discount 折扣配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDiscount(PhotoAlbumDiscount discount) {
        throwIf(discount.getDiscountId() == null, MatchErrorCode.PARAM_ERROR, "折扣配置ID不能为空");
        
        PhotoAlbumDiscount existingDiscount = getById(discount.getDiscountId());
        throwIf(existingDiscount == null, MatchErrorCode.PHOTO_ALBUM_DISCOUNT_NOT_EXISTS);

        validateDiscount(discount);
        updateById(discount);
    }

    /**
     * 删除折扣配置
     *
     * @param discountId 折扣配置ID
     */
    public void deleteDiscount(Long discountId) {
        throwIf(discountId == null, MatchErrorCode.PARAM_ERROR, "折扣配置ID不能为空");
        
        PhotoAlbumDiscount existingDiscount = getById(discountId);
        throwIf(existingDiscount == null, MatchErrorCode.PHOTO_ALBUM_DISCOUNT_NOT_EXISTS);

        removeById(discountId);
    }

    /**
     * 根据相册ID删除所有折扣配置
     *
     * @param albumId 相册ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByAlbumId(Long albumId) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        baseMapper.deleteByAlbumId(albumId);
    }

    /**
     * 根据相册ID获取折扣配置列表
     *
     * @param albumId 相册ID
     * @return 折扣配置列表
     */
    public List<PhotoAlbumDiscount> getDiscountsByAlbumId(Long albumId) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        return baseMapper.selectByAlbumId(albumId);
    }

    /**
     * 根据相册ID获取启用的折扣配置列表
     *
     * @param albumId 相册ID
     * @return 启用的折扣配置列表
     */
    public List<PhotoAlbumDiscount> getEnabledDiscountsByAlbumId(Long albumId) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        
        return baseMapper.selectByAlbumIdAndStatus(albumId, 1);
    }

    /**
     * 根据相册ID和购买数量获取最优折扣配置
     *
     * @param albumId 相册ID
     * @param quantity 购买数量
     * @return 最优折扣配置
     */
    public PhotoAlbumDiscount getBestDiscount(Long albumId, Integer quantity) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        throwIf(quantity == null || quantity <= 0, MatchErrorCode.PARAM_ERROR, "购买数量必须大于0");

        List<PhotoAlbumDiscount> discounts = getEnabledDiscountsByAlbumId(albumId);
        if (CollUtil.isEmpty(discounts)) {
            return null;
        }

        // 找到符合数量要求的最优折扣
        PhotoAlbumDiscount bestDiscount = null;
        for (PhotoAlbumDiscount discount : discounts) {
            if (discount.getQuantity() != null && quantity >= discount.getQuantity()) {
                if (bestDiscount == null || discount.getQuantity() > bestDiscount.getQuantity()) {
                    bestDiscount = discount;
                }
            }
        }

        return bestDiscount;
    }

    /**
     * 计算购买总价
     *
     * @param albumId 相册ID
     * @param quantity 购买数量
     * @param originalSinglePrice 原单价
     * @return 总价
     */
    public BigDecimal calculateTotalPrice(Long albumId, Integer quantity, BigDecimal originalSinglePrice) {
        throwIf(albumId == null, MatchErrorCode.PARAM_ERROR, "相册ID不能为空");
        throwIf(quantity == null || quantity <= 0, MatchErrorCode.PARAM_ERROR, "购买数量必须大于0");
        throwIf(originalSinglePrice == null || originalSinglePrice.compareTo(BigDecimal.ZERO) <= 0, 
                MatchErrorCode.PARAM_ERROR, "原单价必须大于0");

        PhotoAlbumDiscount bestDiscount = getBestDiscount(albumId, quantity);
        if (bestDiscount != null) {
            return bestDiscount.calculateTotalPrice(originalSinglePrice);
        } else {
            // 没有折扣，按原价计算
            return originalSinglePrice.multiply(BigDecimal.valueOf(quantity));
        }
    }

    /**
     * 启用/禁用折扣配置
     *
     * @param discountId 折扣配置ID
     * @param status 状态：0-禁用 1-启用
     */
    public void updateDiscountStatus(Long discountId, Integer status) {
        throwIf(discountId == null, MatchErrorCode.PARAM_ERROR, "折扣配置ID不能为空");
        throwIf(status == null || (status != 0 && status != 1), MatchErrorCode.PARAM_ERROR, "状态参数错误");
        
        PhotoAlbumDiscount existingDiscount = getById(discountId);
        throwIf(existingDiscount == null, MatchErrorCode.PHOTO_ALBUM_DISCOUNT_NOT_EXISTS);

        PhotoAlbumDiscount updateDiscount = new PhotoAlbumDiscount();
        updateDiscount.setDiscountId(discountId);
        updateDiscount.setStatus(status);
        updateById(updateDiscount);
    }

    /**
     * 验证折扣配置
     *
     * @param discount 折扣配置
     */
    private void validateDiscount(PhotoAlbumDiscount discount) {
        throwIf(discount.getQuantity() == null || discount.getQuantity() <= 0, 
                MatchErrorCode.PARAM_ERROR, "购买数量必须大于0");
        
        throwIf(discount.getDiscountType() == null || (discount.getDiscountType() != 1 && discount.getDiscountType() != 2),
                MatchErrorCode.PHOTO_ALBUM_DISCOUNT_CONFIG_ERROR, "折扣类型错误");

        if (discount.isRatioDiscount()) {
            // 按比例折扣
            throwIf(discount.getDiscountRatio() == null ||
                    discount.getDiscountRatio().compareTo(BigDecimal.ZERO) <= 0 ||
                    discount.getDiscountRatio().compareTo(BigDecimal.valueOf(100)) > 0,
                    MatchErrorCode.PHOTO_ALBUM_DISCOUNT_CONFIG_ERROR, "折扣比例必须在0-100之间");
        } else if (discount.isFixedPriceDiscount()) {
            // 固定价格折扣
            throwIf(discount.getDiscountPrice() == null || discount.getDiscountPrice().compareTo(BigDecimal.ZERO) <= 0,
                    MatchErrorCode.PHOTO_ALBUM_DISCOUNT_CONFIG_ERROR, "折扣价格必须大于0");
        }
    }
}
