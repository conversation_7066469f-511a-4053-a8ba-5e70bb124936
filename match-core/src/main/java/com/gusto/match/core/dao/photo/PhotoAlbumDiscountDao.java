package com.gusto.match.core.dao.photo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 相册折扣配置
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
public interface PhotoAlbumDiscountDao extends BaseMapper<PhotoAlbumDiscount> {

    /**
     * 根据相册ID查询折扣配置列表
     *
     * @param albumId 相册ID
     * @return 折扣配置列表
     */
    List<PhotoAlbumDiscount> selectByAlbumId(@Param("albumId") Long albumId);

    /**
     * 根据相册ID和状态查询折扣配置列表
     *
     * @param albumId 相册ID
     * @param status 状态
     * @return 折扣配置列表
     */
    List<PhotoAlbumDiscount> selectByAlbumIdAndStatus(@Param("albumId") Long albumId, @Param("status") Integer status);

    /**
     * 根据相册ID和购买数量查询折扣配置
     *
     * @param albumId 相册ID
     * @param quantity 购买数量
     * @return 折扣配置
     */
    PhotoAlbumDiscount selectByAlbumIdAndQuantity(@Param("albumId") Long albumId, @Param("quantity") Integer quantity);

    /**
     * 根据相册ID删除折扣配置
     *
     * @param albumId 相册ID
     * @return 删除数量
     */
    int deleteByAlbumId(@Param("albumId") Long albumId);
}
