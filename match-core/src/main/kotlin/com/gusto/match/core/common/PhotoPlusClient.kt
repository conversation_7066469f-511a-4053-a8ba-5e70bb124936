package com.gusto.match.core.common

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.http.ForestResponse
import com.gusto.match.model.entity.photo.PhotoPlusCommonRsp
import com.gusto.match.model.entity.photo.PhotoPlusPicture
import com.gusto.match.model.entity.photo.PhotoPlusPictureList

/**
 * <p>
 * 谱时API
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@BaseRequest(baseURL = "#{photo.photo-plus.apiHost}")
interface PhotoPlusClient {

    /**
     * 获取相册图片接口
     *
     * @param activityNo 相册号
     * @param timestamp 当前时间戳
     * @param from 起始位置
     * @param count 请求图片数量
     * @param noWater 是否无水印 true 无水印 false 有水印
     * @param signature 请求签名内容
     */
    @Get("/api/activity/pic/list")
    fun getPictureList(
        @Query("activityNo") activityNo: String,
        @Query("timestamp") timestamp: Long,
        @Query("from") from: Int,
        @Query("count") count: Int,
        @Query("noWater") noWater: Boolean,
        @Query("signature") signature: String
    ): ForestResponse<PhotoPlusCommonRsp<PhotoPlusPictureList>>

    /**
     * 人脸找自己接口
     *
     * @param activityNo 相册号
     * @param timestamp 当前时间戳
     * @param noWater 是否无水印 true 无水印 false 有水印
     * @param url 可访问的图片url
     * @param signature 请求签名内容
     */
    @Get("/api/findme/pic")
    fun findPicturesByFace(
        @Query("activityNo") activityNo: String,
        @Query("timestamp") timestamp: Long,
        @Query("noWater") noWater: String,
        @Query("url") url: String,
        @Query("signature") signature: String
    ): ForestResponse<PhotoPlusCommonRsp<PhotoPlusPictureList>>

    /**
     * 号码牌找自己接口
     *
     * @param activityNo 相册号
     * @param timestamp 当前时间戳
     * @param noWater 是否无水印 true 无水印 false 有水印
     * @param number 查询的字符串
     * @param signature 请求签名内容
     */
    @Get("/api/findme/number")
    fun findPicturesByNumber(
        @Query("activityNo") activityNo: String,
        @Query("timestamp") timestamp: Long,
        @Query("noWater") noWater: String,
        @Query("number") number: String,
        @Query("signature") signature: String
    ): ForestResponse<PhotoPlusCommonRsp<PhotoPlusPictureList>>

    /**
     * 图片详情接口
     *
     * @param activityNo 相册号
     * @param timestamp 当前时间戳
     * @param noWater 是否无水印 true 无水印 false 有水印
     * @param picHash 图片picHash值
     * @param signature 请求签名内容
     */
    @Get("/api/pic")
    fun getPictureDetail(
        @Query("activityNo") activityNo: String,
        @Query("timestamp") timestamp: Long,
        @Query("noWater") noWater: String,
        @Query("picHash") picHash: String,
        @Query("signature") signature: String
    ): ForestResponse<PhotoPlusCommonRsp<List<PhotoPlusPicture>>>

}
