package com.gusto.match.core.common

import cn.hutool.crypto.digest.MD5
import com.gusto.framework.core.exception.ServiceException
import com.gusto.match.core.exception.MatchErrorCode
import com.gusto.match.model.config.PhotoPlusProperties
import com.gusto.match.model.entity.photo.PhotoPlusPicture
import com.gusto.match.model.entity.photo.PhotoPlusPictureList
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 谱时API
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Service
class PhotoPlusService {

    private val logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var properties: PhotoPlusProperties

    @Autowired
    lateinit var client: PhotoPlusClient

    /**
     * 获取相册图片列表
     *
     * @param activityNo 相册号
     * @param from 起始位置
     * @param count 请求图片数量
     * @param noWater 是否无水印
     * @return 图片列表结果
     */
    fun getPictureList(
        activityNo: String,
        from: Int,
        count: Int,
        noWater: Boolean
    ): PhotoPlusPictureList {
        val now = Instant.now().toEpochMilli()
        val paramMap = mapOf(
            "activityNo" to activityNo,
            "timestamp" to now.toString(),
            "from" to from.toString(),
            "count" to count.toString(),
            "noWater" to noWater.toString()
        )
        val signature = genSignature(paramMap, properties.salt)
        val rsp = client.getPictureList(
            activityNo = activityNo,
            timestamp = now,
            from = from,
            count = count,
            noWater = noWater,
            signature = signature
        )
        if (rsp.isError || rsp.result.code != 1) {
            logger.error("[getPictureList] activityNo = $activityNo, rsp content = ${rsp.content}")
            throw ServiceException(MatchErrorCode.PHOTO_PLUS_ERROR.code, "获取相册照片列表失败")
        }
        return rsp.result.result
    }

    /**
     * 通过人脸查找图片
     *
     * @param activityNo 相册号
     * @param imageUrl 可访问的图片url
     * @param noWater 是否无水印
     * @return 图片列表结果
     */
    fun findPicturesByFace(
        activityNo: String,
        imageUrl: String,
        noWater: Boolean = true
    ): PhotoPlusPictureList {
        val now = Instant.now().toEpochMilli()
        val paramMap = mapOf(
            "activityNo" to activityNo,
            "timestamp" to now.toString(),
            "noWater" to noWater.toString(),
            "url" to imageUrl
        )
        val signature = genSignature(paramMap, properties.salt)
        val rsp = client.findPicturesByFace(
            activityNo = activityNo,
            timestamp = now,
            noWater = noWater.toString(),
            url = imageUrl,
            signature = signature
        )
        if (rsp.isError || rsp.result.code != 1) {
            logger.error("[findPicturesByFace] activityNo = $activityNo, imageUrl = $imageUrl, rsp content = ${rsp.content}")
            throw ServiceException(MatchErrorCode.PHOTO_PLUS_ERROR.code, "通过人脸查找照片失败")
        }
        return rsp.result.result
    }

    /**
     * 通过号码牌查找图片
     *
     * @param activityNo 相册号
     * @param number 查询的字符串
     * @param noWater 是否无水印
     * @return 图片列表结果
     */
    fun findPicturesByNumber(
        activityNo: String,
        number: String,
        noWater: Boolean = true
    ): PhotoPlusPictureList {
        val now = Instant.now().toEpochMilli()
        val paramMap = mapOf(
            "activityNo" to activityNo,
            "timestamp" to now.toString(),
            "noWater" to noWater.toString(),
            "number" to number
        )
        val signature = genSignature(paramMap, properties.salt)
        val rsp = client.findPicturesByNumber(
            activityNo = activityNo,
            timestamp = now,
            noWater = noWater.toString(),
            number = number,
            signature = signature
        )
        if (rsp.isError || rsp.result.code != 1) {
            logger.error("[findPicturesByNumber] activityNo = $activityNo, number = $number, rsp content = ${rsp.content}")
            throw ServiceException(MatchErrorCode.PHOTO_PLUS_ERROR.code, "通过人脸查找照片失败")
        }
        return rsp.result.result
    }

    /**
     * 获取图片详情
     *
     * @param activityNo 相册号
     * @param picHash 图片hash值
     * @param noWater 是否无水印
     * @return 图片详情
     */
    fun getPictureDetail(
        activityNo: String,
        picHash: String,
        noWater: Boolean = true
    ): List<PhotoPlusPicture> {
        val now = Instant.now().toEpochMilli()
        val paramMap = mapOf(
            "activityNo" to activityNo,
            "timestamp" to now.toString(),
            "noWater" to noWater.toString(),
            "picHash" to picHash
        )
        val signature = genSignature(paramMap, properties.salt)
        val rsp = client.getPictureDetail(
            activityNo = activityNo,
            timestamp = now,
            noWater = noWater.toString(),
            picHash = picHash,
            signature = signature
        )
        if (rsp.isError || rsp.result.code != 1) {
            logger.error("[getPictureDetail] activityNo = $activityNo, picHash = $picHash, rsp content = ${rsp.content}")
            throw ServiceException(MatchErrorCode.PHOTO_PLUS_ERROR.code, "获取照片失败")
        }
        return rsp.result.result
    }

    /**
     * 生成签名
     */
    fun genSignature(paramMap: Map<String, String>, salt: String): String {
        val filteredParams = paramMap.filterKeys { it != "signature" }
        val sortedKeys = filteredParams.keys.sorted()
        val paramString = sortedKeys.joinToString("&") { key -> "$key=${filteredParams[key]}" }
        val saltedString = paramString + salt
        return MD5.create().digestHex(saltedString)
    }

}