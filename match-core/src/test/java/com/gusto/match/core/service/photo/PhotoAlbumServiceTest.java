package com.gusto.match.core.service.photo;

import com.gusto.match.model.entity.photo.PhotoAlbum;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 相册管理服务测试
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@SpringBootTest
@ActiveProfiles("test")
public class PhotoAlbumServiceTest {

    @Resource
    private PhotoAlbumService photoAlbumService;

    @Resource
    private PhotoAlbumDiscountService photoAlbumDiscountService;

    @Test
    public void testCreateAlbum() {
        // 创建相册
        PhotoAlbum album = new PhotoAlbum();
        album.setOfflineRaceId(1L);
        album.setAlbumName("测试相册");
        album.setAlbumDescription("这是一个测试相册");
        album.setPhotoPlusAlbumIds(Arrays.asList("album001", "album002"));
        album.setPhotographerShareRatio(BigDecimal.valueOf(30));
        album.setCompanyShareRatio(BigDecimal.valueOf(70));
        album.setSinglePhotoPrice(BigDecimal.valueOf(15.00));
        album.setStatus(1);
        album.setCoverImageUrl("https://example.com/cover.jpg");
        album.setSortWeight(100);
        album.setRemark("测试备注");

        Long albumId = photoAlbumService.createAlbum(album);
        assertNotNull(albumId);
        assertTrue(albumId > 0);

        // 验证相册创建成功
        PhotoAlbum createdAlbum = photoAlbumService.getAlbumById(albumId);
        assertNotNull(createdAlbum);
        assertEquals("测试相册", createdAlbum.getAlbumName());
        assertEquals(BigDecimal.valueOf(30), createdAlbum.getPhotographerShareRatio());
        assertEquals(BigDecimal.valueOf(70), createdAlbum.getCompanyShareRatio());
        assertEquals(BigDecimal.valueOf(15.00), createdAlbum.getSinglePhotoPrice());
    }

    @Test
    public void testCreateDiscounts() {
        // 先创建相册
        PhotoAlbum album = new PhotoAlbum();
        album.setOfflineRaceId(1L);
        album.setAlbumName("折扣测试相册");
        album.setPhotoPlusAlbumIds(Arrays.asList("album003"));
        album.setPhotographerShareRatio(BigDecimal.valueOf(40));
        album.setCompanyShareRatio(BigDecimal.valueOf(60));
        album.setSinglePhotoPrice(BigDecimal.valueOf(20.00));

        Long albumId = photoAlbumService.createAlbum(album);

        // 创建折扣配置
        PhotoAlbumDiscount discount1 = new PhotoAlbumDiscount();
        discount1.setQuantity(3);
        discount1.setDiscountType(1);
        discount1.setDiscountRatio(BigDecimal.valueOf(90));
        discount1.setDiscountName("3张9折");

        PhotoAlbumDiscount discount2 = new PhotoAlbumDiscount();
        discount2.setQuantity(5);
        discount2.setDiscountType(1);
        discount2.setDiscountRatio(BigDecimal.valueOf(80));
        discount2.setDiscountName("5张8折");

        List<PhotoAlbumDiscount> discounts = Arrays.asList(discount1, discount2);
        photoAlbumDiscountService.createDiscounts(albumId, discounts);

        // 验证折扣配置创建成功
        List<PhotoAlbumDiscount> createdDiscounts = photoAlbumDiscountService.getDiscountsByAlbumId(albumId);
        assertEquals(2, createdDiscounts.size());
    }

    @Test
    public void testCalculateTotalPrice() {
        // 先创建相册和折扣配置
        PhotoAlbum album = new PhotoAlbum();
        album.setOfflineRaceId(1L);
        album.setAlbumName("价格计算测试相册");
        album.setPhotoPlusAlbumIds(Arrays.asList("album004"));
        album.setPhotographerShareRatio(BigDecimal.valueOf(30));
        album.setCompanyShareRatio(BigDecimal.valueOf(70));
        album.setSinglePhotoPrice(BigDecimal.valueOf(10.00));

        Long albumId = photoAlbumService.createAlbum(album);

        // 创建折扣配置
        PhotoAlbumDiscount discount = new PhotoAlbumDiscount();
        discount.setQuantity(3);
        discount.setDiscountType(1);
        discount.setDiscountRatio(BigDecimal.valueOf(90));
        discount.setDiscountName("3张9折");

        photoAlbumDiscountService.createDiscounts(albumId, Arrays.asList(discount));

        // 测试价格计算
        BigDecimal originalPrice = BigDecimal.valueOf(10.00);
        
        // 购买2张，无折扣
        BigDecimal totalPrice2 = photoAlbumDiscountService.calculateTotalPrice(albumId, 2, originalPrice);
        assertEquals(BigDecimal.valueOf(20.00), totalPrice2);

        // 购买3张，有9折折扣
        BigDecimal totalPrice3 = photoAlbumDiscountService.calculateTotalPrice(albumId, 3, originalPrice);
        assertEquals(BigDecimal.valueOf(27.00), totalPrice3); // 30 * 0.9 = 27

        // 购买5张，应该使用3张的折扣（最优折扣）
        BigDecimal totalPrice5 = photoAlbumDiscountService.calculateTotalPrice(albumId, 5, originalPrice);
        assertEquals(BigDecimal.valueOf(45.00), totalPrice5); // 50 * 0.9 = 45
    }

    @Test
    public void testShareRatioValidation() {
        PhotoAlbum album = new PhotoAlbum();
        album.setPhotographerShareRatio(BigDecimal.valueOf(30));
        album.setCompanyShareRatio(BigDecimal.valueOf(70));
        assertTrue(album.isShareRatioValid());

        album.setCompanyShareRatio(BigDecimal.valueOf(60));
        assertFalse(album.isShareRatioValid());
    }
}
