package com.gusto.match.core.common

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * PhotoPlusService测试类
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
class PhotoPlusServiceTest {
    
    @Test
    fun testSignatureGeneration() {
        // 测试签名生成功能
        val params = mapOf(
            "activityNo" to "7011174",
            "from" to "0",
            "count" to "100",
            "noWater" to "true",
            "timestamp" to "1648632556287"
        )

        val salt = "adui"
        val signature = PhotoPlusSignatureUtils.generateSignature(params, salt)

        // 验证签名不为空且长度正确（MD5是32位）
        assertNotNull(signature)
        assertEquals(32, signature.length)

        // 打印调试信息
        println("生成的签名: $signature")
        println("期望的签名: 4424cb2f79420cf2d223")
    }
    
    @Test
    fun testParameterSorting() {
        // 测试参数排序功能
        val params = mapOf(
            "timestamp" to "1648632556287",
            "activityNo" to "7011174",
            "noWater" to "true",
            "count" to "100",
            "from" to "0"
        )
        
        val salt = "adui"
        val signature = PhotoPlusSignatureUtils.generateSignature(params, salt)
        
        // 无论参数顺序如何，签名结果应该一致
        assertEquals("4424cb2f79420cf2d223", signature)
    }
    
    @Test
    fun testTimestampGeneration() {
        // 测试时间戳生成
        val timestamp1 = PhotoPlusSignatureUtils.getCurrentTimestamp()
        Thread.sleep(1)
        val timestamp2 = PhotoPlusSignatureUtils.getCurrentTimestamp()
        
        // 时间戳应该是递增的
        assertTrue(timestamp1.toLong() < timestamp2.toLong())
        
        // 时间戳应该是13位数字
        assertEquals(13, timestamp1.length)
        assertEquals(13, timestamp2.length)
    }
}
