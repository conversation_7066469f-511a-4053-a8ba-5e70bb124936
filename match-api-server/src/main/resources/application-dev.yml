server:
  port: 10000
  servlet:
    context-path: /api

spring:
  config:
    activate:
      on-profile:
        - dev

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  application:
    name: match-api-server

  datasource:
    name: datasource
    url: *********************************************************************************************************************************************************************************************************************************
    username: gusto_dev_java
    password: X7Hq4)I_Xd5v@k)fs#1$
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      max-active: 200
      initial-size: 20
      max-wait: 30000
      min-idle: 30
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 200
      remove-abandoned: false
      remove-abandoned-timeout: 1800
      log-abandoned: true
      filter:
        # 配置用于监控慢查询 （参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_StatFilter）
        stat:
          enabled: true
          slow-sql-millis: 1000
          log-slow-sql: true
          db-type: mysql
          # 合并语句 优化sql统计功能
          merge-sql: true
        # 日志 使用logback为slf4j (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_LogFilter)
        slf4j:
          enabled: true
          statement-executable-sql-log-enable: true
          statement-parameter-set-log-enabled: false
          statement-execute-query-after-log-enabled: false
          statement-prepare-after-log-enabled: false
          statement-execute-after-log-enabled: false
          statement-prepare-call-after-log-enabled: false
          statement-close-after-log-enabled: false
          statement-execute-batch-after-log-enabled: true
        # 防火墙用于配置预防sql注入 (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE-wallfilter)
        wall:
          enabled: true
          db-type: mysql
          config:
            drop-table-allow: false
            multi-statement-allow: true

  redis:
    database: 0
    host: ************
    port: 36379
    password: gusto123$
    timeout: 3s
    redisson:
      pool-size: 64
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  kafka:
    # 生产者配置
    producer:
      bootstrapServers: localhost:9092    # 服务连接地址
      retries: 0                          # 重试次数, 默认 0 不重试
      compression-type: gzip              # 压缩算法
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        interceptor.classes: com.gusto.match.core.handler.TraceProducerMethodInterceptor
    # 消费者配置
    consumer:
      bootstrap-servers: localhost:9092   # 服务连接地址
      group-id: mygroup                   # 消息组的名称
      auto-offset-reset: latest           # 初始化offset的策略
      enable-auto-commit: true
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        interceptor.classes: com.gusto.match.core.handler.TraceConsumerMethodInterceptor
        spring:
          json:
            trusted:
              packages: com.gusto.match.model.kafka.message
    properties:
      request.timeout.ms: 3000            # 超时时间
      linger.ms: 0                        # 指定 Batch 以时间为策略, 表示延迟多久才发送一次. 0 表示不停留马上发送
      spring:
        json:
          trusted:
            packages: com.gusto.match.model.kafka.message

elasticjob:
  regCenter:
    serverLists: localhost:2181
    namespace: elasticjob-lite-match-api-server
  jobs:
    UserAccompanyRunStartNotifyJob:
      overwrite: true
      elasticJobClass: com.gusto.match.api.job.UserAccompanyRunStartNotifyJob
      cron: 0 0 20 * * ?
      shardingTotalCount: 1
    #    TrainingStudentRunNotifyJob:
    #      overwrite: true
    #      elasticJobClass: com.gusto.match.api.job.TrainingStudentRunNotifyJob
    #      cron: 0 30 21 * * ?
    #      shardingTotalCount: 1
    #    TrainingActivityStartNotifyJob:
    #      overwrite: true
    #      elasticJobClass: com.gusto.match.api.job.TrainingActivityStartNotifyJob
    #      cron: 0 0/5 * * * ?
    #      shardingTotalCount: 1
#    SignUpdateNotifyJob:
#      overwrite: true
#      elasticJobClass: com.gusto.match.api.job.SignCountUpdateNotifyJob
#      cron: 0 0 0 * * ?
#      shardingTotalCount: 1

jetcache:
  statIntervalMinutes: 30
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 0
        maxIdle: 8
      uri: redis://:gusto123$@************:36379/1

logging:
  config: classpath:logback-spring.xml
  level:
    druid.sql.Statement: debug
    com.gusto.match.dao: debug
    com.gusto.framework: debug
    org.springframework.web: debug
    com.gusto.match: debug
    com.gusto: debug
    com.alicp.jetcache: debug
    org.apache.kafka.clients.NetworkClient: error
  file:
    # 日志路径
    path: ./logs
    # 日志名字
    name: match-api-server
    # 日志保留存活天数
    maxHistory: 7
    # 单日志大小(用于分割)
    maxFileSize: 5MB
    # 总日志大小(用于计算文件数量 number = totalSizeCap/maxFileSize )
    totalSizeCap: 100MB

#forest:
#  backend: okhttp3 # 后端HTTP框架（默认为 okhttp3）
#  max-connections: 500 # 连接池最大连接数（默认为 500）
#  max-route-connections: 500 # 每个路由的最大连接数（默认为 500）
#  timeout: 3000 # 请求超时时间，单位为毫秒（默认为 3000）
#  connect-timeout: 3000 # 连接超时时间，单位为毫秒（默认为 timeout）
#  read-timeout: 3000 # 数据读取超时时间，单位为毫秒（默认为 timeout）
#  max-retry-count: 3 # 请求失败后重试次数（默认为 0 次不重试）
#  ssl-protocol: TLSv1.3 # 单向验证的HTTPS的默认SSL协议（默认为 SSLv3）
#  logEnabled: true # 打开或关闭日志（默认为 true）
#  log-request: true # 打开/关闭Forest请求日志（默认为 true）
#  log-response-status: true # 打开/关闭Forest响应状态日志（默认为 true）
#  log-response-content: true # 打开/关闭Forest响应内容日志（默认为 false）
#  variables:
#    pushApiUrl: https://pushdevapi.gusto.cn
#    payPalApiUrl: https://api-m.sandbox.paypal.com

management:
  server:
    port: 10010
  endpoints:
    web:
      exposure:
        include:
          - "prometheus"
          - "info"
  metrics:
    tags:
      application: ${spring.application.name}

gusto:
  xss:
    enable: true
    excludeUrls:
  error-code:
    logLevelMap:
      5000: WARN

# TODO photo update config
photo:
  photo-plus:
    api-host: https://testwxplus.plusx.cn
    salt: cd721e2ce6017f39
