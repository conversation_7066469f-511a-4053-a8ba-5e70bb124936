package com.gusto.match.api.controller.photo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.gusto.framework.core.bean.CommonResult;
import com.gusto.match.core.handler.CheckLogin;
import com.gusto.match.core.service.photo.PhotoAlbumDiscountService;
import com.gusto.match.core.service.photo.PhotoAlbumService;
import com.gusto.match.core.swagger.ApiVersion;
import com.gusto.match.core.swagger.CustomVersion;
import com.gusto.match.model.entity.photo.PhotoAlbum;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;
import com.gusto.match.model.entity.photo.req.CalculatePhotoPriceReq;
import com.gusto.match.model.entity.photo.req.GetPhotoAlbumListReq;
import com.gusto.match.model.entity.photo.rsp.CalculatePhotoPriceRsp;
import com.gusto.match.model.entity.photo.rsp.PhotoAlbumDetailRsp;
import com.gusto.match.model.entity.photo.rsp.PhotoAlbumListRsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 相册管理 用户端控制类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Tag(name = "相册管理")
@Validated
@RestController
@RequestMapping("photo/album")
public class PhotoAlbumController {

    @Autowired
    private PhotoAlbumService photoAlbumService;

    @Autowired
    private PhotoAlbumDiscountService photoAlbumDiscountService;

    /**
     * 获取线下赛相册列表
     */
    @ApiVersion({CustomVersion.OFFLINE})
    @Operation(summary = "获取线下赛相册列表")
    @GetMapping("list")
    public CommonResult<List<PhotoAlbumListRsp>> getAlbumList(@Valid GetPhotoAlbumListReq req) {
        List<PhotoAlbum> albums = photoAlbumService.getEnabledAlbumsByOfflineRaceId(req.getOfflineRaceId());
        
        List<PhotoAlbumListRsp> result = albums.stream()
                .map(album -> BeanUtil.copyProperties(album, PhotoAlbumListRsp.class))
                .collect(Collectors.toList());
        
        return CommonResult.success(result);
    }

    /**
     * 获取相册详情
     */
    @ApiVersion({CustomVersion.OFFLINE})
    @Operation(summary = "获取相册详情")
    @GetMapping("detail/{albumId}")
    public CommonResult<PhotoAlbumDetailRsp> getAlbumDetail(
            @Parameter(description = "相册ID") @PathVariable Long albumId) {
        PhotoAlbum album = photoAlbumService.getAlbumById(albumId);
        
        // 只返回启用的相册
        if (!album.isEnabled()) {
            return CommonResult.success(null);
        }
        
        List<PhotoAlbumDiscount> discounts = photoAlbumDiscountService.getEnabledDiscountsByAlbumId(albumId);
        
        PhotoAlbumDetailRsp result = BeanUtil.copyProperties(album, PhotoAlbumDetailRsp.class);
        if (CollUtil.isNotEmpty(discounts)) {
            List<PhotoAlbumDetailRsp.PhotoAlbumDiscountRsp> discountRsps = discounts.stream()
                    .map(discount -> BeanUtil.copyProperties(discount, PhotoAlbumDetailRsp.PhotoAlbumDiscountRsp.class))
                    .collect(Collectors.toList());
            result.setDiscounts(discountRsps);
        }
        
        return CommonResult.success(result);
    }

    /**
     * 计算照片价格
     */
    @ApiVersion({CustomVersion.OFFLINE})
    @Operation(summary = "计算照片价格")
    @PostMapping("calculate-price")
    public CommonResult<CalculatePhotoPriceRsp> calculatePrice(@Valid @RequestBody CalculatePhotoPriceReq req) {
        PhotoAlbum album = photoAlbumService.getAlbumById(req.getAlbumId());
        
        // 只处理启用的相册
        if (!album.isEnabled()) {
            return CommonResult.success(null);
        }
        
        BigDecimal originalSinglePrice = album.getSinglePhotoPrice();
        BigDecimal originalTotalPrice = originalSinglePrice.multiply(BigDecimal.valueOf(req.getQuantity()));
        
        // 获取最优折扣
        PhotoAlbumDiscount bestDiscount = photoAlbumDiscountService.getBestDiscount(req.getAlbumId(), req.getQuantity());
        
        CalculatePhotoPriceRsp result = new CalculatePhotoPriceRsp();
        result.setAlbumId(req.getAlbumId());
        result.setQuantity(req.getQuantity());
        result.setOriginalSinglePrice(originalSinglePrice);
        result.setOriginalTotalPrice(originalTotalPrice);
        
        if (bestDiscount != null) {
            // 有折扣
            BigDecimal actualTotalPrice = photoAlbumDiscountService.calculateTotalPrice(
                    req.getAlbumId(), req.getQuantity(), originalSinglePrice);
            BigDecimal discountAmount = originalTotalPrice.subtract(actualTotalPrice);
            
            result.setActualTotalPrice(actualTotalPrice);
            result.setDiscountAmount(discountAmount);
            result.setHasDiscount(true);
            
            // 设置折扣信息
            CalculatePhotoPriceRsp.DiscountInfo discountInfo = new CalculatePhotoPriceRsp.DiscountInfo();
            discountInfo.setDiscountId(bestDiscount.getDiscountId());
            discountInfo.setDiscountName(bestDiscount.getDiscountName());
            discountInfo.setDiscountType(bestDiscount.getDiscountType());
            discountInfo.setDiscountRatio(bestDiscount.getDiscountRatio());
            discountInfo.setDiscountPrice(bestDiscount.getDiscountPrice());
            result.setDiscountInfo(discountInfo);
        } else {
            // 无折扣
            result.setActualTotalPrice(originalTotalPrice);
            result.setDiscountAmount(BigDecimal.ZERO);
            result.setHasDiscount(false);
        }
        
        return CommonResult.success(result);
    }
}
